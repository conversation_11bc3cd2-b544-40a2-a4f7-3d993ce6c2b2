{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken,\n    loading: authLoading\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  // Debug logging\n  useEffect(() => {\n    if (user) {\n      console.log('AuthSection - User object:', user);\n      console.log('AuthSection - User metadata:', user.user_metadata);\n    }\n  }, [user]);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        // Store state for verification\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (!storedState || storedState !== state) {\n        throw new Error('Invalid state parameter');\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        toast.success('Successfully logged in with Facebook!');\n\n        // Redirect to dashboard after successful authentication\n        window.location.href = '/dashboard';\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  // Show loading state while auth is loading\n  if (authLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status loading\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  }\n  if (isAuthenticated && user) {\n    var _user$user_metadata, _user$user_metadata2;\n    // Extract user data safely from Supabase user object\n    const firstName = (user === null || user === void 0 ? void 0 : (_user$user_metadata = user.user_metadata) === null || _user$user_metadata === void 0 ? void 0 : _user$user_metadata.first_name) || (user === null || user === void 0 ? void 0 : user.firstName) || 'User';\n    const lastName = (user === null || user === void 0 ? void 0 : (_user$user_metadata2 = user.user_metadata) === null || _user$user_metadata2 === void 0 ? void 0 : _user$user_metadata2.last_name) || (user === null || user === void 0 ? void 0 : user.lastName) || '';\n    const email = (user === null || user === void 0 ? void 0 : user.email) || 'No email';\n    const role = (user === null || user === void 0 ? void 0 : user.role) || 'user';\n    const facebookConnected = (user === null || user === void 0 ? void 0 : user.facebookConnected) || false;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [firstName, \" \", lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), facebookConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#1877f2'\n              },\n              children: \"\\u2705 Facebook Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Facebook, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), \"Login with Facebook\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"GVu+0NMeeu4T3JT/ZNhjZ3pFZr0=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "loading", "authLoading", "activeTab", "setActiveTab", "showToken", "setShowToken", "setLoading", "console", "log", "user_metadata", "loginForm", "registerForm", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "pathname", "response", "getOAuthUrl", "oauthUrl", "localStorage", "setItem", "href", "Error", "error", "message", "storedState", "getItem", "removeItem", "handleOAuthCallback", "tokens", "accessToken", "refreshToken", "JSON", "stringify", "success", "url", "URL", "searchParams", "delete", "history", "replaceState", "document", "title", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_user$user_metadata", "_user$user_metadata2", "firstName", "first_name", "lastName", "last_name", "email", "role", "facebookConnected", "size", "style", "color", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken, loading: authLoading } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  // Debug logging\n  useEffect(() => {\n    if (user) {\n      console.log('AuthSection - User object:', user);\n      console.log('AuthSection - User metadata:', user.user_metadata);\n    }\n  }, [user]);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    \n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      \n      if (response.data.oauthUrl) {\n        // Store state for verification\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n      \n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (!storedState || storedState !== state) {\n        throw new Error('Invalid state parameter');\n      }\n      \n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      \n      const redirectUri = `${window.location.origin}${window.location.pathname}`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        toast.success('Successfully logged in with Facebook!');\n\n        // Redirect to dashboard after successful authentication\n        window.location.href = '/dashboard';\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      \n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  // Show loading state while auth is loading\n  if (authLoading) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status loading\">\n          <p>Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (isAuthenticated && user) {\n    // Extract user data safely from Supabase user object\n    const firstName = user?.user_metadata?.first_name || user?.firstName || 'User';\n    const lastName = user?.user_metadata?.last_name || user?.lastName || '';\n    const email = user?.email || 'No email';\n    const role = user?.role || 'user';\n    const facebookConnected = user?.facebookConnected || false;\n\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{firstName} {lastName}</strong></p>\n              <p>{email}</p>\n              <p>Role: {role}</p>\n              {facebookConnected && (\n                <p style={{color: '#1877f2'}}>✅ Facebook Connected</p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button \n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button \n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button \n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>\n              <Facebook size={20} />\n              Login with Facebook\n            </h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n          \n          <button \n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n          \n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,QAAQ;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACpG,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,OAAO,EAAEM,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIc,IAAI,EAAE;MACRa,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEd,IAAI,CAAC;MAC/Ca,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEd,IAAI,CAACe,aAAa,CAAC;IACjE;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;EAEV,MAAMgB,SAAS,GAAG5B,OAAO,CAAC,CAAC;EAC3B,MAAM6B,YAAY,GAAG7B,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAMgC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,IAAID,IAAI,IAAIE,KAAK,IAAI,CAACxB,eAAe,EAAE;MACrCyB,sBAAsB,CAACH,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACxB,eAAe,CAAC,CAAC;EAErB,MAAM0B,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9BhB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMV,KAAK,CAAC0B,IAAI,CAAC;IACjBhB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiB,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjChB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,QAAQ,CAACyB,IAAI,CAAC;IACpBhB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMkB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAGZ,MAAM,CAACC,QAAQ,CAACY,QAAQ,EAAE;MAC1E,MAAMC,QAAQ,GAAG,MAAM7C,WAAW,CAAC8C,WAAW,CAACJ,WAAW,CAAC;MAE3D,IAAIG,QAAQ,CAACN,IAAI,CAACQ,QAAQ,EAAE;QAC1B;QACAC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEJ,QAAQ,CAACN,IAAI,CAACH,KAAK,CAAC;QACjEL,MAAM,CAACC,QAAQ,CAACkB,IAAI,GAAGL,QAAQ,CAACN,IAAI,CAACQ,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd7B,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAAC8C,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMhB,sBAAsB,GAAG,MAAAA,CAAOH,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM+B,WAAW,GAAGN,YAAY,CAACO,OAAO,CAAC,sBAAsB,CAAC;MAChE,IAAI,CAACD,WAAW,IAAIA,WAAW,KAAKlB,KAAK,EAAE;QACzC,MAAM,IAAIe,KAAK,CAAC,yBAAyB,CAAC;MAC5C;;MAEA;MACAH,YAAY,CAACQ,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAMd,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAGZ,MAAM,CAACC,QAAQ,CAACY,QAAQ,EAAE;MAC1E,MAAMC,QAAQ,GAAG,MAAM7C,WAAW,CAACyD,mBAAmB,CAACvB,IAAI,EAAEE,KAAK,EAAEM,WAAW,CAAC;MAEhF,IAAIG,QAAQ,CAACN,IAAI,CAAC5B,IAAI,IAAIkC,QAAQ,CAACN,IAAI,CAACmB,MAAM,EAAE;QAC9C;QACAV,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEJ,QAAQ,CAACN,IAAI,CAACmB,MAAM,CAACC,WAAW,CAAC;QACrEX,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACN,IAAI,CAACmB,MAAM,CAACE,YAAY,CAAC;QACvEZ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEY,IAAI,CAACC,SAAS,CAACjB,QAAQ,CAACN,IAAI,CAAC5B,IAAI,CAAC,CAAC;QAEhEL,KAAK,CAACyD,OAAO,CAAC,uCAAuC,CAAC;;QAEtD;QACAhC,MAAM,CAACC,QAAQ,CAACkB,IAAI,GAAG,YAAY;MACrC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd7B,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAAC8C,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;;MAEtD;MACA,MAAMW,GAAG,GAAG,IAAIC,GAAG,CAAClC,MAAM,CAACC,QAAQ,CAAC;MACpCgC,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;MAC/BH,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;MAChCpC,MAAM,CAACqC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEP,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;;EAED;EACA,IAAItD,WAAW,EAAE;IACf,oBACEV,OAAA;MAAKiE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BlE,OAAA;QAAAkE,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BtE,OAAA;QAAKiE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClClE,OAAA;UAAAkE,QAAA,EAAG;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlE,eAAe,IAAID,IAAI,EAAE;IAAA,IAAAoE,mBAAA,EAAAC,oBAAA;IAC3B;IACA,MAAMC,SAAS,GAAG,CAAAtE,IAAI,aAAJA,IAAI,wBAAAoE,mBAAA,GAAJpE,IAAI,CAAEe,aAAa,cAAAqD,mBAAA,uBAAnBA,mBAAA,CAAqBG,UAAU,MAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,SAAS,KAAI,MAAM;IAC9E,MAAME,QAAQ,GAAG,CAAAxE,IAAI,aAAJA,IAAI,wBAAAqE,oBAAA,GAAJrE,IAAI,CAAEe,aAAa,cAAAsD,oBAAA,uBAAnBA,oBAAA,CAAqBI,SAAS,MAAIzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,EAAE;IACvE,MAAME,KAAK,GAAG,CAAA1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,KAAK,KAAI,UAAU;IACvC,MAAMC,IAAI,GAAG,CAAA3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,IAAI,KAAI,MAAM;IACjC,MAAMC,iBAAiB,GAAG,CAAA5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,iBAAiB,KAAI,KAAK;IAE1D,oBACE/E,OAAA;MAAKiE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BlE,OAAA;QAAAkE,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BtE,OAAA;QAAKiE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxClE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA,CAACP,IAAI;YAACuF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAAkE,QAAA,eAAGlE,OAAA;gBAAAkE,QAAA,GAASO,SAAS,EAAC,GAAC,EAACE,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9CtE,OAAA;cAAAkE,QAAA,EAAIW;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdtE,OAAA;cAAAkE,QAAA,GAAG,QAAM,EAACY,IAAI;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClBS,iBAAiB,iBAChB/E,OAAA;cAAGiF,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAhB,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlE,OAAA;YAAKiE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlE,OAAA;cAAAkE,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBtE,OAAA;cACEmF,OAAO,EAAEA,CAAA,KAAMrE,YAAY,CAAC,CAACD,SAAS,CAAE;cACxCoD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBrD,SAAS,gBAAGb,OAAA,CAACJ,MAAM;gBAACoF,IAAI,EAAE;cAAG;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACL,GAAG;gBAACqF,IAAI,EAAE;cAAG;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLzD,SAAS,iBACRb,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlE,OAAA;cAAAkE,QAAA,EAAO1D,QAAQ,CAAC;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENtE,OAAA;UAAQmF,OAAO,EAAE5E,MAAO;UAAC0D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7ClE,OAAA,CAACN,MAAM;YAACsF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtE,OAAA;IAAKiE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BlE,OAAA;MAAAkE,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvBtE,OAAA;MAAKiE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlE,OAAA;QACEiE,SAAS,EAAEtD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,UAAU,CAAE;QAAAsD,QAAA,gBAExClE,OAAA,CAACH,QAAQ;UAACmF,IAAI,EAAE;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA;QACEiE,SAAS,EAAEtD,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjDwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,OAAO,CAAE;QAAAsD,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA;QACEiE,SAAS,EAAEtD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,UAAU,CAAE;QAAAsD,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL3D,SAAS,KAAK,UAAU,gBACvBX,OAAA;MAAKiE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrClE,OAAA;QAAKiE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClClE,OAAA;UAAAkE,QAAA,gBACElE,OAAA,CAACH,QAAQ;YAACmF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtE,OAAA;UAAAkE,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAAkE,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCtE,OAAA;YAAAkE,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtE,OAAA;YAAAkE,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCtE,OAAA;YAAAkE,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCtE,OAAA;YAAAkE,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENtE,OAAA;QACEmF,OAAO,EAAElD,mBAAoB;QAC7BmD,QAAQ,EAAE3E,OAAQ;QAClBwD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9BlE,OAAA,CAACH,QAAQ;UAACmF,IAAI,EAAE;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrB7D,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETtE,OAAA;QAAGiE,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAAlE,OAAA;UAAAkE,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAAtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJ3D,SAAS,KAAK,OAAO,gBACvBX,OAAA;MAAMqF,QAAQ,EAAElE,SAAS,CAACmE,YAAY,CAACxD,OAAO,CAAE;MAACmC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpElE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBtE,OAAA;UACEuF,IAAI,EAAC,OAAO;UAAA,GACRpE,SAAS,CAACb,QAAQ,CAAC,OAAO,EAAE;YAAEkF,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACd,KAAK,iBAC/B7E,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACd,KAAK,CAAChC;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBtE,OAAA;UACEuF,IAAI,EAAC,UAAU;UAAA,GACXpE,SAAS,CAACb,QAAQ,CAAC,UAAU,EAAE;YAAEkF,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDnD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClC5F,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/C,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC/C;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAQuF,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE3E,OAAQ;QAACwD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DzD,OAAO,GAAG,eAAe,GAAG;MAAO;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEPtE,OAAA;MAAMqF,QAAQ,EAAEjE,YAAY,CAACkE,YAAY,CAACtD,UAAU,CAAE;MAACiC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1ElE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BtE,OAAA;UACEuF,IAAI,EAAC,MAAM;UAAA,GACPnE,YAAY,CAACd,QAAQ,CAAC,WAAW,EAAE;YAAEkF,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDlD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAAClB,SAAS,iBACtCzE,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9C,YAAY,CAACsE,SAAS,CAACC,MAAM,CAAClB,SAAS,CAAC5B;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBtE,OAAA;UACEuF,IAAI,EAAC,MAAM;UAAA,GACPnE,YAAY,CAACd,QAAQ,CAAC,UAAU,EAAE;YAAEkF,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACDlD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAAChB,QAAQ,iBACrC3E,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9C,YAAY,CAACsE,SAAS,CAACC,MAAM,CAAChB,QAAQ,CAAC9B;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBtE,OAAA;UACEuF,IAAI,EAAC,OAAO;UAAA,GACRnE,YAAY,CAACd,QAAQ,CAAC,OAAO,EAAE;YAAEkF,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDlD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACd,KAAK,iBAClC7E,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9C,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACd,KAAK,CAAChC;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBtE,OAAA;UACEuF,IAAI,EAAC,UAAU;UAAA,GACXnE,YAAY,CAACd,QAAQ,CAAC,UAAU,EAAE;YACpCkF,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAEjD,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACF4C,WAAW,EAAC;QAAwC;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDlD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrC5F,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9C,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC/C;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCtE,OAAA;UACEuF,IAAI,EAAC,KAAK;UAAA,GACNnE,YAAY,CAACd,QAAQ,CAAC,OAAO,CAAC;UAClCmF,WAAW,EAAC;QAAyB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtE,OAAA;QAAQuF,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE3E,OAAQ;QAACwD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DzD,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpE,EAAA,CA5TID,WAAW;EAAA,QAC4EX,OAAO,EAahFC,OAAO,EACJA,OAAO;AAAA;AAAAwG,EAAA,GAfxB9F,WAAW;AA8TjB,eAAeA,WAAW;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
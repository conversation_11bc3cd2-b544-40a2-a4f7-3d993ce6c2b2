<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Ads Templates - Pressure Max</title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <style>
        /* Additional styles for Facebook Ads page */
        .platform-tabs {
            display: flex;
            gap: 2px;
            background: #1a1a1a;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 32px;
            border: 1px solid #2a2a2a;
        }

        .platform-tab {
            padding: 12px 24px;
            background: transparent;
            border: none;
            color: #888888;
            cursor: pointer;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .platform-tab.active {
            background: #3b82f6;
            color: white;
        }

        .platform-tab:hover:not(.active) {
            background: #2a2a2a;
            color: #ffffff;
        }

        .search-and-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            gap: 20px;
        }

        .template-search {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .template-search i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666666;
            width: 16px;
            height: 16px;
        }

        .template-search input {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            padding: 12px 16px 12px 48px;
            color: #ffffff;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .template-search input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .template-search input::placeholder {
            color: #666666;
        }

        .template-actions {
            display: flex;
            gap: 12px;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 28px;
            margin-bottom: 40px;
        }

        .template-card {
            background: #111111;
            border: 1px solid #1f1f1f;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .template-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .template-image {
            width: 100%;
            height: 220px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .template-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .template-badge {
            position: absolute;
            top: 12px;
            left: 12px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .template-content {
            padding: 24px;
        }

        .template-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .template-stats {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #888888;
        }

        .template-stat {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .template-actions-row {
            display: flex;
            gap: 8px;
        }

        .template-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .template-btn-primary {
            background: #3b82f6;
            color: white;
        }

        .template-btn-primary:hover {
            background: #2563eb;
        }

        .template-btn-secondary {
            background: #1a1a1a;
            color: #cccccc;
            border: 1px solid #2a2a2a;
        }

        .template-btn-secondary:hover {
            background: #2a2a2a;
            color: #ffffff;
        }

        .platform-icon {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: #888888;
            margin-top: 8px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Template categories */
        .template-pressure-washing .template-image {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .template-home-services .template-image {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .template-cleaning .template-image {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .template-maintenance .template-image {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .template-seasonal .template-image {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .template-promotional .template-image {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        /* Emergency layout fix */
        .main-content {
            opacity: 1 !important;
            visibility: visible !important;
            display: flex !important;
            flex-direction: column !important;
            margin-left: 280px !important;
        }

        #sidebar-container {
            width: 280px !important;
            height: 100vh !important;
            position: fixed !important;
            left: 0 !important;
            top: 0 !important;
            z-index: 100 !important;
            background: #111111 !important;
        }

        #header-container {
            height: 72px !important;
            background: #111111 !important;
            border-bottom: 1px solid #1f1f1f !important;
        }
    </style>
</head>
<body data-page="facebook-ads">
    <div class="app">
        <!-- Sidebar Container -->
        <div id="sidebar-container"></div>

        <!-- Main Content -->
        <main class="main-content" style="opacity: 1 !important; visibility: visible !important; margin-left: 280px !important;">
            <!-- Header Container -->
            <div id="header-container"></div>

            <!-- Facebook Ads Content -->
            <div class="dashboard">
                <div class="dashboard-header">
                    <div class="dashboard-title">
                        <h1>Facebook Ads Templates</h1>
                        <p>Choose from proven ad templates for pressure washing and home services</p>
                    </div>
                    
                    <div class="dashboard-actions">
                        <button class="btn btn-secondary">
                            <i data-lucide="upload"></i>
                            Import Templates
                        </button>
                        <button class="btn btn-primary">
                            <i data-lucide="plus"></i>
                            Create Custom
                        </button>
                    </div>
                </div>

                <!-- Platform Tabs -->
                <div class="platform-tabs">
                    <button class="platform-tab active">
                        <i data-lucide="globe"></i>
                        All Platforms
                    </button>
                    <button class="platform-tab">
                        <i data-lucide="facebook"></i>
                        Facebook & Instagram
                    </button>
                    <button class="platform-tab">
                        <i data-lucide="search"></i>
                        Google Ads
                    </button>
                </div>

                <!-- Search and Actions -->
                <div class="search-and-actions">
                    <div class="template-search">
                        <i data-lucide="search"></i>
                        <input type="text" placeholder="Search for template" id="templateSearch">
                    </div>
                    
                    <div class="template-actions">
                        <button class="btn btn-secondary btn-sm">
                            <i data-lucide="filter"></i>
                            Filter
                        </button>
                        <button class="btn btn-primary btn-sm">
                            <i data-lucide="download"></i>
                            Import All Templates
                        </button>
                    </div>
                </div>

                <!-- Templates Grid - Row 1 -->
                <div class="section-title">
                    <i data-lucide="droplets"></i>
                    Pressure Washing Templates
                </div>
                <div class="templates-grid">
                    <div class="template-card template-pressure-washing" data-template="pressure-washing-basic">
                        <div class="template-image">
                            <div class="template-badge">Most Popular</div>
                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop" alt="Pressure Washing">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">1500 sq. ft. Pressure Washing Special</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 463 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-cleaning" data-template="monthly-cleaning">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=200&fit=crop" alt="Monthly Cleaning">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Monthly Exterior Cleaning - Just $47</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 452 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-home-services" data-template="custom-pressure-washing">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=300&h=200&fit=crop" alt="Custom Pressure Washing">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Pressure Washing | Custom Pricing</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 463 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-maintenance" data-template="mold-removal">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop" alt="Mold Removal">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Mold Gone, Shine On! Home Revival</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 135 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Templates Grid - Row 2 -->
                <div class="section-title">
                    <i data-lucide="home"></i>
                    Home Services Templates
                </div>
                <div class="templates-grid">
                    <div class="template-card template-seasonal" data-template="spring-cleaning">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=300&h=200&fit=crop" alt="Spring Cleaning">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Spring Pressure Washing Special - Just $89</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 135 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-promotional" data-template="whole-house-softwash">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=300&h=200&fit=crop" alt="Whole House SoftWash">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Whole House SoftWash | Start Today</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 463 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-home-services" data-template="deck-cleaning">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300&h=200&fit=crop" alt="Deck Cleaning">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Deck & Patio Restoration Service</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 287 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-card template-maintenance" data-template="gutter-cleaning">
                        <div class="template-image">
                            <img src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop" alt="Gutter Cleaning">
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">Professional Gutter Cleaning Service</h3>
                            <div class="template-stats">
                                <div class="template-stat">
                                    <i data-lucide="trending-up"></i>
                                    <span>Imported 198 times</span>
                                </div>
                            </div>
                            <div class="template-actions-row">
                                <button class="template-btn template-btn-primary">Launch Campaign</button>
                                <button class="template-btn template-btn-secondary">Details</button>
                            </div>
                            <div class="platform-icon">
                                <i data-lucide="facebook"></i>
                                <span>Facebook / Instagram</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="/components/shared-utils.js"></script>
    <script src="/components/component-loader.js"></script>
    <script src="/components/smooth-navigation.js"></script>
    <script src="/script.js"></script>
    <script src="/facebook-ads.js"></script>
</body>
</html>

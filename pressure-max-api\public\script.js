// Initialize Lucide icons and dashboard
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();

    // Initialize dashboard functionality (only for dashboard page)
    const pageId = document.body.getAttribute('data-page');
    if (pageId === 'dashboard') {
        initializeDashboard();
    }
});

function initializeDashboard() {
    // Task card interactions (dashboard-specific)
    const taskCards = document.querySelectorAll('.task-card');
    taskCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add task card click logic here
            console.log('Task card clicked:', this.querySelector('.task-title').textContent);
        });
    });

    // Dashboard action buttons (dashboard-specific)
    const dashboardBtns = document.querySelectorAll('.dashboard-actions .btn');
    dashboardBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.textContent.trim();
            console.log('Dashboard button clicked:', text);

            if (text.includes('Export')) {
                showNotification('Export functionality coming soon!', 'info');
            } else if (text.includes('Create Campaign')) {
                showNotification('Redirecting to campaign creation...', 'success');
                // Could redirect to campaign creation page
            }
        });
    });

    // Board control buttons (dashboard-specific)
    const boardBtns = document.querySelectorAll('.board-controls .btn');
    boardBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.textContent.trim();
            console.log('Board button clicked:', text);

            if (text.includes('Filter')) {
                showNotification('Filter options coming soon!', 'info');
            } else if (text.includes('Date Range')) {
                showNotification('Date range picker coming soon!', 'info');
            }
        });
    });

    // Load real data
    loadDashboardData();
}

// These functions are now in shared-utils.js and available globally

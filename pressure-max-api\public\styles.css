/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    line-height: 1.5;
    overflow-x: hidden;
}

/* App Layout */
.app {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #111111;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
}

.sidebar-header {
    height: 72px;
    padding: 0 20px;
    border-bottom: 1px solid #1f1f1f;
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 24px;
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-label {
    font-size: 12px;
    font-weight: 500;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 20px;
    margin-bottom: 12px;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #cccccc;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
}

.nav-link:hover {
    background: #1a1a1a;
    color: #ffffff;
}

.nav-item.active .nav-link {
    background: #1a1a1a;
    color: #ffffff;
    border-right: 2px solid #3b82f6;
}

.nav-link i {
    width: 20px;
    height: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #1f1f1f;
}

.new-project-btn {
    width: 100%;
    padding: 12px 16px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background 0.2s ease;
}

.new-project-btn:hover {
    background: #2563eb;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    height: 72px;
    background: #111111;
    border-bottom: 1px solid #1f1f1f;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    position: sticky;
    top: 0;
    z-index: 50;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-container i {
    position: absolute;
    left: 12px;
    color: #666666;
    width: 16px;
    height: 16px;
}

.search-input {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 10px 12px 10px 40px;
    color: #ffffff;
    font-size: 14px;
    width: 300px;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.search-input::placeholder {
    color: #666666;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-btn {
    position: relative;
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.2s ease;
}

.header-btn:hover {
    background: #1a1a1a;
    color: #ffffff;
}

.header-btn.logout-btn:hover {
    background: #dc2626;
    color: #ffffff;
}

/* Component Loading States */
#sidebar-container, #header-container {
    transition: opacity 0.15s ease;
}

#sidebar-container.loading, #header-container.loading {
    opacity: 0;
}

/* Prevent flash of unstyled content */
.main-content {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.main-content.components-ready {
    opacity: 1;
}

/* Ensure components load before main content */
body:not(.components-loaded) .main-content {
    visibility: hidden;
}

body.components-loaded .main-content {
    visibility: visible;
    opacity: 1;
}

/* Fallback: Show content after 3 seconds even if components don't load */
.main-content {
    animation: showContentFallback 0.1s ease 3s forwards;
}

@keyframes showContentFallback {
    to {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

/* Sidebar Loading Skeleton */
#sidebar-container.loading::before {
    content: '';
    display: block;
    width: 280px;
    height: 100vh;
    background: #111111;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
}

/* Header Loading Skeleton */
#header-container.loading::before {
    content: '';
    display: block;
    width: 100%;
    height: 72px;
    background: #0a0a0a;
    border-bottom: 1px solid #1f1f1f;
    position: relative;
    z-index: 99;
}

/* Smooth page transitions */
.main-content {
    transition: all 0.2s ease;
    opacity: 1;
}

.main-content.transitioning {
    opacity: 0.7;
    transform: translateY(5px);
}

/* Prevent layout shift */
.app {
    min-height: 100vh;
    position: relative;
}

/* Page transition overlay */
.page-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 10, 10, 0.8);
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.15s ease;
}

.page-transition-overlay.active {
    opacity: 1;
    pointer-events: all;
}

/* Loading spinner for page transitions */
.page-loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 2px solid #333;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Faster component loading */
#sidebar-container, #header-container {
    transition: opacity 0.15s ease;
}

/* Preload hint for faster navigation */
.nav-link {
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    transition: background 0.1s ease;
}

.nav-link:hover::after {
    background: rgba(255, 255, 255, 0.05);
}

.notification-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Dashboard */
.dashboard {
    flex: 1;
    padding: 32px;
    background: #0a0a0a;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
}

.dashboard-title h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #ffffff;
}

.dashboard-title p {
    color: #888888;
    font-size: 16px;
}

.dashboard-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #1a1a1a;
    color: #cccccc;
    border: 1px solid #2a2a2a;
}

.btn-secondary:hover {
    background: #2a2a2a;
    color: #ffffff;
}

.btn-sm {
    padding: 8px 12px;
    font-size: 13px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 12px;
    padding: 24px;
    transition: border-color 0.2s ease;
}

.stat-card:hover {
    border-color: #2a2a2a;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-label {
    font-size: 14px;
    color: #888888;
    font-weight: 500;
}

.stat-icon {
    width: 20px;
    height: 20px;
    color: #3b82f6;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
}

.stat-change {
    font-size: 14px;
    font-weight: 500;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

/* Project Board */
.project-board {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 12px;
    padding: 24px;
}

.board-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.board-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
}

.board-controls {
    display: flex;
    gap: 12px;
}

/* Kanban Board */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    min-height: 600px;
}

.kanban-column {
    background: #0a0a0a;
    border-radius: 8px;
    padding: 16px;
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #1f1f1f;
}

.column-title {
    font-weight: 600;
    color: #ffffff;
}

.column-count {
    background: #2a2a2a;
    color: #cccccc;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.column-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Task Cards */
.task-card {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-card:hover {
    border-color: #3a3a3a;
    transform: translateY(-1px);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.task-category {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-category.ui-design {
    background: #fef3c7;
    color: #92400e;
}

.task-category.marketing {
    background: #d1fae5;
    color: #065f46;
}

.task-category.ux-research {
    background: #ddd6fe;
    color: #5b21b6;
}

.task-category.testing {
    background: #fce7f3;
    color: #be185d;
}

.task-category.graphic-design {
    background: #e0f2fe;
    color: #0c4a6e;
}

.task-menu {
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    font-size: 16px;
}

.task-menu:hover {
    background: #2a2a2a;
    color: #cccccc;
}

.task-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    line-height: 1.4;
}

.task-description {
    font-size: 13px;
    color: #888888;
    line-height: 1.4;
    margin-bottom: 16px;
}

.task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-avatars {
    display: flex;
    gap: -8px;
}

.task-avatars img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #1a1a1a;
    margin-left: -8px;
}

.task-avatars img:first-child {
    margin-left: 0;
}

.task-stats {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #666666;
}

.task-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.task-stats i {
    width: 12px;
    height: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .search-input {
        width: 200px;
    }
    
    .kanban-board {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .template-search input {
        width: 100%;
    }

    .search-and-actions {
        flex-direction: column;
        gap: 16px;
    }

    .template-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .dashboard {
        padding: 20px;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .templates-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .platform-tabs {
        flex-direction: column;
    }

    .platform-tab {
        text-align: center;
    }

    .search-input {
        width: 150px;
    }

    .header {
        padding: 0 20px;
    }
}

/**
 * Smooth Navigation System
 * Provides seamless page transitions with preloading
 */

class SmoothNavigation {
    constructor() {
        this.isNavigating = false;
        this.preloadedPages = new Map();
        this.transitionOverlay = null;
        this.init();
    }

    init() {
        this.createTransitionOverlay();
        this.preloadCommonPages();
        this.setupNavigationInterception();
        this.setupHoverPreloading();
    }

    /**
     * Create transition overlay for smooth navigation
     */
    createTransitionOverlay() {
        this.transitionOverlay = document.createElement('div');
        this.transitionOverlay.className = 'page-transition-overlay';
        this.transitionOverlay.innerHTML = '<div class="page-loading-spinner"></div>';
        document.body.appendChild(this.transitionOverlay);
    }

    /**
     * Preload common pages in the background
     */
    async preloadCommonPages() {
        const commonPages = [
            { url: '/', key: 'dashboard' },
            { url: '/facebook-ads', key: 'facebook-ads' },
            { url: '/testing', key: 'testing' }
        ];

        // Preload pages with a small delay to not impact initial load
        setTimeout(async () => {
            for (const page of commonPages) {
                try {
                    await this.preloadPage(page.url, page.key);
                } catch (error) {
                    console.log(`Could not preload ${page.url}:`, error.message);
                }
            }
        }, 2000);
    }

    /**
     * Preload a page and cache its content
     */
    async preloadPage(url, key) {
        if (this.preloadedPages.has(key)) {
            return this.preloadedPages.get(key);
        }

        try {
            const response = await fetch(url, {
                credentials: 'include',
                headers: {
                    'Accept': 'text/html'
                }
            });

            if (response.ok) {
                const html = await response.text();
                this.preloadedPages.set(key, html);
                console.log(`📄 Preloaded page: ${url}`);
                return html;
            }
        } catch (error) {
            console.log(`Failed to preload ${url}:`, error.message);
        }
        return null;
    }

    /**
     * Setup navigation interception for smooth transitions
     */
    setupNavigationInterception() {
        // Intercept navigation links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (!link) return;

            const href = link.getAttribute('href');
            
            // Only handle internal navigation
            if (this.shouldInterceptNavigation(href)) {
                e.preventDefault();
                this.navigateToPage(href);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.smoothNavigation) {
                this.navigateToPage(window.location.pathname, false);
            }
        });
    }

    /**
     * Setup hover preloading for instant navigation
     */
    setupHoverPreloading() {
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (!link) return;

            const href = link.getAttribute('href');
            if (this.shouldInterceptNavigation(href)) {
                const pageKey = this.getPageKey(href);
                if (!this.preloadedPages.has(pageKey)) {
                    this.preloadPage(href, pageKey);
                }
            }
        });
    }

    /**
     * Check if navigation should be intercepted
     */
    shouldInterceptNavigation(href) {
        if (!href || href === '#') return false;
        if (href.startsWith('http') && !href.includes('localhost:3000')) return false;
        if (href.startsWith('mailto:') || href.startsWith('tel:')) return false;
        return true;
    }

    /**
     * Get page key for caching
     */
    getPageKey(url) {
        if (url === '/' || url === '') return 'dashboard';
        return url.replace('/', '').replace(/[^a-zA-Z0-9]/g, '-');
    }

    /**
     * Navigate to page with smooth transition
     */
    async navigateToPage(url, updateHistory = true) {
        if (this.isNavigating) return;
        
        this.isNavigating = true;
        const pageKey = this.getPageKey(url);

        try {
            // Start transition
            this.startTransition();

            // Try to use preloaded content first
            let html = this.preloadedPages.get(pageKey);
            
            if (!html) {
                // Fetch page if not preloaded
                const response = await fetch(url, {
                    credentials: 'include',
                    headers: {
                        'Accept': 'text/html'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                html = await response.text();
            }

            // Update page content
            await this.updatePageContent(html);

            // Update browser history
            if (updateHistory) {
                history.pushState(
                    { smoothNavigation: true }, 
                    '', 
                    url
                );
            }

            // End transition
            this.endTransition();

        } catch (error) {
            console.error('Navigation error:', error);
            // Fallback to regular navigation
            window.location.href = url;
        } finally {
            this.isNavigating = false;
        }
    }

    /**
     * Start page transition
     */
    startTransition() {
        // Add transition class to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('transitioning');
        }

        // Show overlay
        this.transitionOverlay.classList.add('active');
    }

    /**
     * Update page content
     */
    async updatePageContent(html) {
        // Parse new HTML
        const parser = new DOMParser();
        const newDoc = parser.parseFromString(html, 'text/html');
        
        // Extract main content
        const newMainContent = newDoc.querySelector('.main-content');
        const currentMainContent = document.querySelector('.main-content');
        
        if (newMainContent && currentMainContent) {
            // Replace main content
            currentMainContent.innerHTML = newMainContent.innerHTML;
            
            // Update page title
            const newTitle = newDoc.querySelector('title');
            if (newTitle) {
                document.title = newTitle.textContent;
            }

            // Update body data-page attribute
            const newBodyPage = newDoc.body.getAttribute('data-page');
            if (newBodyPage) {
                document.body.setAttribute('data-page', newBodyPage);
            }

            // Reinitialize components for new content
            if (window.componentLoader) {
                await window.componentLoader.initializePage(newBodyPage || 'unknown');
            }

            // Reinitialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // Reinitialize page-specific scripts
            this.reinitializePageScripts();
        }
    }

    /**
     * End page transition
     */
    endTransition() {
        setTimeout(() => {
            // Remove transition class
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.classList.remove('transitioning');
            }

            // Hide overlay
            this.transitionOverlay.classList.remove('active');
        }, 100);
    }

    /**
     * Reinitialize page-specific scripts
     */
    reinitializePageScripts() {
        // Reinitialize dashboard functionality if on dashboard
        const pageId = document.body.getAttribute('data-page');
        if (pageId === 'dashboard' && typeof initializeDashboard === 'function') {
            setTimeout(initializeDashboard, 100);
        }

        // Reinitialize Facebook Ads functionality if on facebook-ads page
        if (pageId === 'facebook-ads' && typeof initializeFacebookAds === 'function') {
            setTimeout(initializeFacebookAds, 100);
        }
    }

    /**
     * Clear preloaded cache (useful for development)
     */
    clearCache() {
        this.preloadedPages.clear();
        console.log('🗑️ Navigation cache cleared');
    }
}

// Initialize smooth navigation when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other scripts to load
    setTimeout(() => {
        window.smoothNavigation = new SmoothNavigation();
        console.log('🚀 Smooth navigation initialized');
    }, 500);
});

// Export for manual control
window.SmoothNavigation = SmoothNavigation;

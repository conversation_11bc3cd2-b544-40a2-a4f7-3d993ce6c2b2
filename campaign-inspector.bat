@echo off
echo ========================================
echo    CAMPAIGN RAW DATA INSPECTOR
echo ========================================
echo.

:: Set working directory to script location
cd /d "%~dp0"

echo 🔍 Starting Campaign Inspector...
echo.

:: Check if API is already running
echo 🔍 Checking if API server is running...
netstat -an | findstr ":3000 " >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API server is already running on port 3000
    echo.
    echo 🌐 Opening Campaign Inspector...
    timeout /t 2 /nobreak >nul
    start http://localhost:3000/testing
    echo.
    echo 📋 CAMPAIGN INSPECTOR FEATURES:
    echo    🔍 Select Ad Account → Select Campaign → Inspect Raw Data
    echo    📊 Two inspection modes:
    echo       • Standard: Fast cached data
    echo       • Ultra-Detailed: Complete Facebook API data
    echo    📄 View complete campaign hierarchy with all fields
    echo    🎯 Targeting details, creative info, and performance insights
    echo.
) else (
    echo ⚠️  API server is not running. Starting it now...
    echo.
    
    :: Start API server
    echo 🔧 Starting Backend API Server...
    start "Pressure Max API" cmd /k "cd pressure-max-api && echo ========================================= && echo    PRESSURE MAX API SERVER && echo ========================================= && echo. && echo 🧪 Testing Interface: http://localhost:3000/testing && echo 🔍 Campaign Inspector: Available in Testing Interface && echo. && npm start"
    
    echo ⏳ Waiting for API server to start...
    timeout /t 8 /nobreak >nul
    
    echo 🌐 Opening Campaign Inspector...
    start http://localhost:3000/testing
    echo.
    echo 📋 CAMPAIGN INSPECTOR FEATURES:
    echo    🔍 Select Ad Account → Select Campaign → Inspect Raw Data
    echo    📊 Two inspection modes:
    echo       • Standard: Fast cached data  
    echo       • Ultra-Detailed: Complete Facebook API data
    echo    📄 View complete campaign hierarchy with all fields
    echo    🎯 Targeting details, creative info, and performance insights
    echo.
)

echo 💡 USAGE TIPS:
echo    1. Select an ad account from the dropdown
echo    2. Choose a campaign to inspect
echo    3. Click "Inspect Campaign" for standard data
echo    4. Click "Get Ultra-Detailed Raw Data" for complete API data
echo    5. Scroll down to see the complete JSON output
echo.
echo Press any key to close this launcher...
pause >nul

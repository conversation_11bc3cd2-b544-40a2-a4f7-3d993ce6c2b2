// cSpell:words orbitron
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Zap,
  MessageSquare,
  Target,
  Star,
  ArrowRight,
  Phone,
  Mail,
  MapPin,
  Shield,
  BarChart3,
  Play,
  Users,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react';

function Landing() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 500);
    return () => clearTimeout(timer);
  }, []);

  const features = [
    {
      icon: Target,
      title: "AI-Powered Ad Templates",
      subtitle: "20+ Proven Templates",
      description: "Ready-to-deploy Facebook ad templates specifically designed for pressure washing businesses. Each template is optimized for maximum lead generation and tested across thousands of campaigns.",
      metrics: [
        "3.2% average CTR",
        "$12 average cost per lead",
        "28% conversion rate"
      ]
    },
    {
      icon: MessageSquare,
      title: "Automated Lead Follow-up",
      subtitle: "Instant Response",
      description: "Never miss a lead with our automated follow-up system that nurtures prospects and converts them into customers.",
      metrics: [
        "5-minute response time",
        "85% contact rate",
        "40% appointment booking rate"
      ]
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      subtitle: "Data-Driven Insights",
      description: "Track your ROI with detailed analytics and insights on campaign performance, lead quality, and conversion rates.",
      metrics: [
        "Real-time reporting",
        "ROI tracking",
        "Lead quality scoring"
      ]
    }
  ];

  const testimonials = [
    {
      name: "Mike Rodriguez",
      business: "Rodriguez Pressure Wash",
      rating: 5,
      text: "This platform tripled my revenue in just 30 days. It's like having a dedicated marketing team working around the clock.",
      results: "+300% Revenue",
      status: "Verified Customer"
    },
    {
      name: "Sarah Chen",
      business: "Crystal Clean Systems",
      rating: 5,
      text: "I'm saving 3 hours of marketing work every day. The AI handles everything flawlessly, letting me focus on my customers.",
      results: "3+ Hours Saved Per Day",
      status: "Verified Customer"
    },
    {
      name: "David Thompson",
      business: "Thompson Power Washing",
      rating: 5,
      text: "The return on investment is incredible. I'm getting more qualified leads than I ever thought possible. This is the future of marketing for our industry.",
      results: "+500% ROI",
      status: "Verified Customer"
    }
  ];

  const pricingPlans = [
    {
      name: "Starter",
      price: "$298",
      period: "/month",
      description: "Perfect for getting started and automating your lead flow.",
      features: [
        "AI-Powered Lead Engagement",
        "Basic Ad Templates",
        "Email Support",
        "100 leads/month"
      ],
      cta: "Start Free Trial",
      popular: false,
    },
    {
      name: "Growth",
      price: "$598",
      period: "/month",
      description: "For businesses ready to scale their marketing efforts.",
      features: [
        "Everything in Starter, plus:",
        "Targeted Ad Campaigns",
        "Automated Content Creation",
        "Priority Support",
        "500 leads/month"
      ],
      cta: "Get Started",
      popular: true,
    },
    {
      name: "Scale",
      price: "$998",
      period: "/month",
      description: "The ultimate solution for market leaders.",
      features: [
        "Everything in Growth, plus:",
        "Advanced Analytics",
        "Dedicated Account Manager",
        "Unlimited leads",
        "Custom Integrations"
      ],
      cta: "Contact Us",
      popular: false,
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden relative">
      {/* Background Pattern */}
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-cyan-500/5" />
        <div className="circuit-pattern" />
      </div>

      {/* Main Content */}
      <div className={`transition-opacity duration-1000 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>

        {/* Header */}
        <header className="fixed w-full z-50 bg-black/80 backdrop-blur-sm border-b border-cyan-500/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center relative">
                  <Zap className="text-black" size={24} />
                </div>
                <span className="text-2xl font-bold font-orbitron tracking-wider text-cyan-400">
                  PressureMax
                </span>
              </div>

              <nav className="hidden md:flex items-center space-x-8">
                <a href="#features" className="nav-link">Features</a>
                <a href="#testimonials" className="nav-link">Testimonials</a>
                <a href="#pricing" className="nav-link">Pricing</a>
                <Link to="/login" className="nav-link">
                  Sign In
                </Link>
                <Link to="/signup" className="header-login-btn">
                  Get Started Free
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Enhanced Hero Section */}
        <section className="min-h-screen flex items-center justify-center relative pt-20 overflow-hidden">
          <div className="absolute inset-0 bg-black"></div>
          <div className="absolute inset-0 opacity-30">
            <div className="circuit-bg w-full h-full"></div>
          </div>

          {/* Floating particles */}
          <div className="particles">
            <div className="particle" style={{left: '10%', animationDelay: '0s'}}></div>
            <div className="particle" style={{left: '20%', animationDelay: '1s'}}></div>
            <div className="particle" style={{left: '30%', animationDelay: '2s'}}></div>
            <div className="particle" style={{left: '40%', animationDelay: '3s'}}></div>
            <div className="particle" style={{left: '50%', animationDelay: '4s'}}></div>
            <div className="particle" style={{left: '60%', animationDelay: '5s'}}></div>
            <div className="particle" style={{left: '70%', animationDelay: '0.5s'}}></div>
            <div className="particle" style={{left: '80%', animationDelay: '1.5s'}}></div>
            <div className="particle" style={{left: '90%', animationDelay: '2.5s'}}></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="animate-fade-in-up">
                    <div className="inline-flex items-center px-4 py-2 bg-cyan-500/10 border border-cyan-500/30 rounded-full text-cyan-400 text-sm font-semibold">
                      <Zap size={16} className="mr-2 animate-glow" />
                      #1 Marketing Automation Platform
                    </div>
                  </div>

                  <h1 className="text-4xl md:text-6xl font-bold font-orbitron tracking-wider leading-tight animate-fade-in-up delay-200">
                    Transform Your Business with
                    <span className="block gradient-text text-shadow mt-2">
                      AI-Powered Marketing
                    </span>
                  </h1>

                  <p className="text-xl text-gray-300 leading-relaxed animate-fade-in-up delay-400">
                    Join 2,500+ pressure washing businesses generating <span className="text-cyan-400 font-bold">$50M+ in revenue</span> through
                    our intelligent automation platform. Stop chasing leads—let them come to you.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up delay-600">
                  <a href="#pricing" className="cta-button btn-3d">
                    <span>Start Free Trial</span>
                    <ArrowRight size={20} />
                  </a>
                  <button className="demo-button">
                    <Play size={18} className="mr-2" />
                    Watch Demo (2 min)
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center animate-fade-in-up delay-800">
                  <div className="glass p-4 card-hover">
                    <div className="text-3xl font-bold gradient-text animate-count-up">+150%</div>
                    <div className="text-gray-400 text-sm">Revenue Growth</div>
                    <div className="progress-bar mt-2">
                      <div className="progress-fill" style={{'--progress-width': '85%'}}></div>
                    </div>
                  </div>
                  <div className="glass p-4 card-hover delay-100">
                    <div className="text-3xl font-bold gradient-text animate-count-up delay-200">15+</div>
                    <div className="text-gray-400 text-sm">Hours Saved Weekly</div>
                    <div className="progress-bar mt-2">
                      <div className="progress-fill" style={{'--progress-width': '92%'}}></div>
                    </div>
                  </div>
                  <div className="glass p-4 card-hover delay-200">
                    <div className="text-3xl font-bold gradient-text animate-count-up delay-400">3x</div>
                    <div className="text-gray-400 text-sm">More Qualified Leads</div>
                    <div className="progress-bar mt-2">
                      <div className="progress-fill" style={{'--progress-width': '78%'}}></div>
                    </div>
                  </div>
                </div>

                {/* Trust indicators */}
                <div className="flex items-center space-x-6 text-gray-400 text-sm animate-fade-in-up delay-1000">
                  <div className="flex items-center">
                    <Shield size={16} className="text-green-400 mr-2" />
                    SOC 2 Compliant
                  </div>
                  <div className="flex items-center">
                    <Star size={16} className="text-yellow-400 mr-2" />
                    4.9/5 Rating
                  </div>
                  <div className="flex items-center">
                    <Users size={16} className="text-cyan-400 mr-2" />
                    2,500+ Customers
                  </div>
                </div>
              </div>

              <div className="relative animate-fade-in-right delay-600">
                <div className="relative z-10 glass-strong p-8 card-hover animate-float">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-cyan-400 font-semibold">Live Dashboard</span>
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-400">
                      <Clock size={12} />
                      <span>Real-time</span>
                    </div>
                  </div>

                  {/* Enhanced dashboard preview */}
                  <div className="bg-gradient-to-br from-gray-900 to-black p-6 rounded-lg border border-cyan-500/20 mb-6">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold gradient-text">$47,250</div>
                        <div className="text-xs text-gray-400">Monthly Revenue</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">+23%</div>
                        <div className="text-xs text-gray-400">Growth Rate</div>
                      </div>
                    </div>

                    <div className="h-20 bg-gradient-to-r from-cyan-500/20 to-transparent rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-cyan-400/20 to-cyan-300/10 animate-pulse"></div>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-500 to-cyan-300"></div>
                    </div>
                  </div>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 flex items-center">
                        <TrendingUp size={14} className="mr-2 text-cyan-400" />
                        AI Engagement:
                      </span>
                      <span className="text-green-400 font-semibold">Active</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 flex items-center">
                        <Users size={14} className="mr-2 text-cyan-400" />
                        New Leads Today:
                      </span>
                      <span className="text-cyan-400 font-bold">23</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 flex items-center">
                        <BarChart3 size={14} className="mr-2 text-cyan-400" />
                        Conversion Rate:
                      </span>
                      <span className="text-green-400 font-semibold">34.7%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 flex items-center">
                        <CheckCircle size={14} className="mr-2 text-cyan-400" />
                        System Status:
                      </span>
                      <span className="text-green-400 font-semibold">Operational</span>
                    </div>
                  </div>
                </div>

                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-transparent border-2 border-cyan-500/30 transform rotate-2 -z-10 animate-glow"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Features Section */}
        <section id="features" className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900/50 to-black/50"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center mb-16 reveal">
              <div className="inline-flex items-center px-4 py-2 bg-cyan-500/10 border border-cyan-500/30 rounded-full text-cyan-400 text-sm font-semibold mb-6">
                <Star size={16} className="mr-2" />
                Complete Marketing Suite
              </div>
              <h2 className="text-3xl md:text-5xl font-bold font-orbitron tracking-wider mb-4 gradient-text">
                Everything You Need to <span className="text-cyan-400">Dominate</span> Your Market
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Our AI-powered platform combines lead generation, customer engagement, and campaign optimization
                into one seamless experience that grows your business 24/7.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className={`glass-strong card-hover p-8 group reveal delay-${(index + 1) * 200}`}>
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center mb-6 relative animate-glow group-hover:animate-float">
                        <Icon className="text-black" size={32} />
                        <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 to-cyan-600/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h3 className="text-2xl font-bold font-orbitron tracking-wider text-white mb-2 group-hover:text-cyan-400 transition-colors">
                            {feature.title}
                          </h3>
                          <div className="text-cyan-400 font-bold text-sm mb-4 bg-cyan-500/10 px-3 py-1 rounded-full inline-block">
                            {feature.subtitle}
                          </div>
                        </div>

                        <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors">
                          {feature.description}
                        </p>

                        <div className="space-y-3 pt-4 border-t border-cyan-500/20">
                          {feature.metrics.map((metric, idx) => (
                            <div key={idx} className="flex items-center space-x-3 group-hover:translate-x-2 transition-transform duration-300" style={{transitionDelay: `${idx * 50}ms`}}>
                              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                              <span className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">{metric}</span>
                            </div>
                          ))}
                        </div>

                        {/* Progress indicator */}
                        <div className="mt-6 pt-4 border-t border-cyan-500/10">
                          <div className="flex justify-between items-center text-xs text-gray-500 mb-2">
                            <span>Implementation</span>
                            <span>95%</span>
                          </div>
                          <div className="progress-bar">
                            <div className="progress-fill" style={{'--progress-width': '95%'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20 bg-gradient-to-br from-gray-900 to-black relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold font-orbitron tracking-wider mb-4">
                What Our <span className="text-cyan-400">Customers</span> Say
              </h2>
              <p className="text-xl text-gray-300">Join a community of successful business owners.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="bg-black border border-cyan-500/30 p-8 hover:border-cyan-500 transition-all duration-300 relative group">
                  <div className="absolute top-4 right-4">
                    <div className="text-xs text-cyan-400 bg-cyan-500/10 px-2 py-1 border border-cyan-500/30">
                      {testimonial.status}
                    </div>
                  </div>

                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="text-cyan-400 fill-current" size={16} />
                    ))}
                  </div>

                  <p className="text-gray-300 mb-6 leading-relaxed text-sm">
                    "{testimonial.text}"
                  </p>

                  <div className="border-t border-cyan-500/20 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-bold text-white">{testimonial.name}</div>
                        <div className="text-gray-400 text-sm">{testimonial.business}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-cyan-400 font-bold">{testimonial.results}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Network Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">2,500+</div>
                <div className="text-gray-400 text-sm">Businesses Served</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">$50M+</div>
                <div className="text-gray-400 text-sm">Revenue Generated</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">98%</div>
                <div className="text-gray-400 text-sm">Customer Satisfaction</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">24/7</div>
                <div className="text-gray-400 text-sm">Support</div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold font-orbitron tracking-wider mb-4">
                Simple, Transparent <span className="text-cyan-400">Pricing</span>
              </h2>
              <p className="text-xl text-gray-300">Choose the plan that's right for your business.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {pricingPlans.map((plan) => (
                <div key={plan.name} className={`relative bg-black border-2 p-8 transition-all duration-300 hover:shadow-lg ${
                  plan.popular
                    ? 'border-cyan-500 bg-cyan-500/5 scale-105 shadow-cyan-500/25'
                    : 'border-cyan-500/30 hover:border-cyan-500'
                  }`}>
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-2 font-bold text-sm">
                        MOST POPULAR
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold font-orbitron tracking-wider text-white mb-4">{plan.name}</h3>
                    <p className="text-gray-400 mb-6 text-sm">{plan.description}</p>
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-white">{plan.price}</span>
                      <span className="text-gray-400 ml-2">{plan.period}</span>
                    </div>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => window.location.href = '/login'}
                    className={`w-full block text-center ${
                      plan.popular
                        ? 'cta-button'
                        : 'demo-button'
                    }`}
                  >
                    {plan.cta}
                  </button>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <p className="text-gray-400 mb-4">All plans include a 14-day free trial. No credit card required.</p>
              <div className="flex items-center justify-center space-x-8 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>Secure & Reliable</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>30-Day Money-Back Guarantee</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>Free Onboarding Support</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced CTA Section */}
        <section className="py-32 bg-gradient-to-br from-cyan-900/20 to-black relative overflow-hidden">
          <div className="absolute inset-0 circuit-bg opacity-20"></div>
          <div className="particles">
            <div className="particle" style={{left: '15%', animationDelay: '0s'}}></div>
            <div className="particle" style={{left: '35%', animationDelay: '2s'}}></div>
            <div className="particle" style={{left: '55%', animationDelay: '4s'}}></div>
            <div className="particle" style={{left: '75%', animationDelay: '1s'}}></div>
          </div>

          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div className="glass-strong p-16 rounded-2xl border border-cyan-500/30 animate-glow">
              <div className="inline-flex items-center px-4 py-2 bg-cyan-500/20 border border-cyan-500/40 rounded-full text-cyan-400 text-sm font-semibold mb-8">
                <Zap size={16} className="mr-2 animate-pulse" />
                Limited Time: 50% Off First 3 Months
              </div>

              <h2 className="text-3xl md:text-5xl font-bold font-orbitron tracking-wider text-white mb-6 gradient-text">
                Ready to <span className="text-cyan-400">10x Your Revenue</span>?
              </h2>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto">
                Join 2,500+ pressure washing businesses generating <span className="text-cyan-400 font-bold">$50M+ in revenue</span> through
                our AI-powered automation platform. Your competitors are already using it.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <a href="#pricing" className="cta-button btn-3d text-lg px-10 py-4">
                  <span>Start Free Trial</span>
                  <ArrowRight size={20} />
                </a>
                <button className="demo-button text-lg px-10 py-4">
                  <Play size={18} className="mr-2" />
                  Watch Demo
                </button>
              </div>

              <div className="flex items-center justify-center space-x-8 text-gray-400 text-sm">
                <div className="flex items-center">
                  <CheckCircle size={16} className="text-green-400 mr-2" />
                  No Credit Card Required
                </div>
                <div className="flex items-center">
                  <CheckCircle size={16} className="text-green-400 mr-2" />
                  14-Day Free Trial
                </div>
                <div className="flex items-center">
                  <CheckCircle size={16} className="text-green-400 mr-2" />
                  Cancel Anytime
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-black border-t border-cyan-500/20 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center">
                    <Zap className="text-black" size={24} />
                  </div>
                  <span className="text-2xl font-bold font-orbitron tracking-wider text-cyan-400">PressureMax</span>
                </div>
                <p className="text-gray-400 leading-relaxed text-sm">
                  The all-in-one marketing platform for pressure washing businesses.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 font-orbitron text-cyan-400">Product</h3>
                <ul className="space-y-2 text-gray-400 text-sm">
                  <li><a href="#features" className="footer-link">Features</a></li>
                  <li><a href="#pricing" className="footer-link">Pricing</a></li>
                  <li><a href="/integrations" className="footer-link">Integrations</a></li>
                  <li><a href="/testing" className="footer-link">API Docs</a></li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 font-orbitron text-cyan-400">Resources</h3>
                <ul className="space-y-2 text-gray-400 text-sm">
                  <li><a href="/blog" className="footer-link">Blog</a></li>
                  <li><a href="/case-studies" className="footer-link">Case Studies</a></li>
                  <li><a href="/help" className="footer-link">Help Center</a></li>
                  <li><a href="/webinars" className="footer-link">Webinars</a></li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 font-orbitron text-cyan-400">Contact</h3>
                <ul className="space-y-3 text-gray-400 text-sm">
                  <li className="flex items-center space-x-3">
                    <Phone size={16} className="text-cyan-400" />
                    <span>(*************</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <Mail size={16} className="text-cyan-400" />
                    <span><EMAIL></span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <MapPin size={16} className="text-cyan-400" />
                    <span>Austin, TX</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="border-t border-cyan-500/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2024 PressureMax. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm text-gray-400 mt-4 md:mt-0">
                <a href="/privacy" className="footer-link">Privacy Policy</a>
                <a href="/terms" className="footer-link">Terms of Service</a>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}

export default Landing;
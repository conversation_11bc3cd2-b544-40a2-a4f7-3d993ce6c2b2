{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useFacebook } from '../contexts/FacebookContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home, Settings, TrendingUp, Users, DollarSign, Eye, TestTube } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const {\n    isConnected: facebookConnected\n  } = useFacebook();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalCampaigns: 0,\n    activeAds: 0,\n    totalSpend: 0,\n    totalLeads: 0\n  });\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n  const handleGoToTesting = () => {\n    navigate('/testing');\n  };\n  useEffect(() => {\n    // Load dashboard stats\n    // This would typically fetch from your API\n    setStats({\n      totalCampaigns: 12,\n      activeAds: 8,\n      totalSpend: 2450.75,\n      totalLeads: 156\n    });\n  }, []);\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE80 Pressure Max API Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Comprehensive testing interface for the Pressure Max API backend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-user-section\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Welcome, \", user.firstName || user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoToLanding,\n              className: \"nav-btn\",\n              children: [/*#__PURE__*/_jsxDEV(Home, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), \"Landing\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"logout-btn\",\n              children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sections-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(AuthSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(FacebookConnectionSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(CampaignSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(LeadFormsSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(ApiTestingSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"dashboard-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Pressure Max API Testing Interface - Built with React\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"http://localhost:3000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"u71PATsgeCIY9grbCYtDAjyz+v8=\", false, function () {\n  return [useAuth, useFacebook, useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useFacebook", "useNavigate", "LogOut", "User", "Home", "Settings", "TrendingUp", "Users", "DollarSign", "Eye", "TestTube", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "isAuthenticated", "logout", "isConnected", "facebookConnected", "navigate", "stats", "setStats", "totalCampaigns", "activeAds", "totalSpend", "totalLeads", "handleLogout", "handleGoToLanding", "handleGoToTesting", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "email", "onClick", "AuthSection", "FacebookConnectionSection", "CampaignSection", "LeadFormsSection", "ApiTestingSection", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useFacebook } from '../contexts/FacebookContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home, Settings, TrendingUp, Users, DollarSign, Eye, TestTube } from 'lucide-react';\n\nconst Dashboard = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const { isConnected: facebookConnected } = useFacebook();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalCampaigns: 0,\n    activeAds: 0,\n    totalSpend: 0,\n    totalLeads: 0\n  });\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n\n  const handleGoToTesting = () => {\n    navigate('/testing');\n  };\n\n  useEffect(() => {\n    // Load dashboard stats\n    // This would typically fetch from your API\n    setStats({\n      totalCampaigns: 12,\n      activeAds: 8,\n      totalSpend: 2450.75,\n      totalLeads: 156\n    });\n  }, []);\n\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n\n  return (\n    <div className=\"dashboard\">\n      {/* Dashboard Header */}\n      <header className=\"dashboard-header\">\n        <div className=\"dashboard-header-content\">\n          <div className=\"dashboard-title\">\n            <h1>🚀 Pressure Max API Dashboard</h1>\n            <p>Comprehensive testing interface for the Pressure Max API backend</p>\n          </div>\n          \n          <div className=\"dashboard-user-section\">\n            {user && (\n              <div className=\"user-info\">\n                <User size={16} />\n                <span>Welcome, {user.firstName || user.email}</span>\n              </div>\n            )}\n            \n            <div className=\"dashboard-actions\">\n              <button onClick={handleGoToLanding} className=\"nav-btn\">\n                <Home size={16} />\n                Landing\n              </button>\n              <button onClick={handleLogout} className=\"logout-btn\">\n                <LogOut size={16} />\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Dashboard Main Content */}\n      <main className=\"dashboard-main\">\n        <div className=\"sections-container\">\n          <section className=\"section\">\n            <AuthSection />\n          </section>\n\n          <section className=\"section\">\n            <FacebookConnectionSection />\n          </section>\n\n          <section className=\"section\">\n            <CampaignSection />\n          </section>\n\n          <section className=\"section\">\n            <LeadFormsSection />\n          </section>\n\n          <section className=\"section\">\n            <ApiTestingSection />\n          </section>\n        </div>\n      </main>\n\n      {/* Dashboard Footer */}\n      <footer className=\"dashboard-footer\">\n        <p>Pressure Max API Testing Interface - Built with React</p>\n        <p>Backend API: <code>http://localhost:3000</code></p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1G,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEmB,WAAW,EAAEC;EAAkB,CAAC,GAAGnB,WAAW,CAAC,CAAC;EACxD,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC;IACjC0B,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMV,MAAM,CAAC,CAAC;IACdG,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9BT,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACd;IACA;IACAwB,QAAQ,CAAC;MACPC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACV,eAAe,EAAE;IACpBI,QAAQ,CAAC,UAAU,CAAC;IACpB,OAAO,IAAI;EACb;EAEA,oBACER,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnB,OAAA;MAAQkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAClCnB,OAAA;QAAKkB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCnB,OAAA;UAAKkB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnB,OAAA;YAAAmB,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCvB,OAAA;YAAAmB,QAAA,EAAG;UAAgE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpChB,IAAI,iBACHH,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnB,OAAA,CAACT,IAAI;cAACiC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClBvB,OAAA;cAAAmB,QAAA,GAAM,WAAS,EAAChB,IAAI,CAACsB,SAAS,IAAItB,IAAI,CAACuB,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN,eAEDvB,OAAA;YAAKkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCnB,OAAA;cAAQ2B,OAAO,EAAEX,iBAAkB;cAACE,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACrDnB,OAAA,CAACR,IAAI;gBAACgC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cAAQ2B,OAAO,EAAEZ,YAAa;cAACG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACnDnB,OAAA,CAACV,MAAM;gBAACkC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTvB,OAAA;MAAMkB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BnB,OAAA;QAAKkB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCnB,OAAA;UAASkB,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BnB,OAAA,CAAC4B,WAAW;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEVvB,OAAA;UAASkB,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BnB,OAAA,CAAC6B,yBAAyB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEVvB,OAAA;UAASkB,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BnB,OAAA,CAAC8B,eAAe;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEVvB,OAAA;UAASkB,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BnB,OAAA,CAAC+B,gBAAgB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEVvB,OAAA;UAASkB,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BnB,OAAA,CAACgC,iBAAiB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvB,OAAA;MAAQkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAClCnB,OAAA;QAAAmB,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5DvB,OAAA;QAAAmB,QAAA,GAAG,eAAa,eAAAnB,OAAA;UAAAmB,QAAA,EAAM;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrB,EAAA,CAxGID,SAAS;EAAA,QAC6Bd,OAAO,EACNC,WAAW,EACrCC,WAAW;AAAA;AAAA4C,EAAA,GAHxBhC,SAAS;AA0Gf,eAAeA,SAAS;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
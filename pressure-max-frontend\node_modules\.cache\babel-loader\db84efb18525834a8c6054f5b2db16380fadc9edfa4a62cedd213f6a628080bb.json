{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Eye, EyeOff, Mail, Lock, User, ArrowRight, Zap, CheckCircle } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Signup() {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState(false);\n  const {\n    signUp\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    if (!validateForm()) {\n      setLoading(false);\n      return;\n    }\n    try {\n      await signUp(formData.email, formData.password, {\n        first_name: formData.firstName,\n        last_name: formData.lastName,\n        full_name: `${formData.firstName} ${formData.lastName}`\n      });\n      setSuccess(true);\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 2000);\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (success) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-black flex items-center justify-center relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 circuit-bg opacity-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-container relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glass-strong p-8 rounded-2xl border border-cyan-500/30 text-center animate-scale-in\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6\",\n            children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"text-green-400\",\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold font-orbitron text-white mb-4\",\n            children: \"Account Created Successfully!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 mb-6\",\n            children: \"Welcome to PressureMax! Please check your email to verify your account.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-pulse text-cyan-400\",\n            children: \"Redirecting to dashboard...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black flex items-center justify-center relative overflow-hidden py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 circuit-bg opacity-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"particles\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: '10%',\n          animationDelay: '0s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: '30%',\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: '50%',\n          animationDelay: '4s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: '70%',\n          animationDelay: '1s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: '90%',\n          animationDelay: '3s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '500px',\n        width: '100%',\n        margin: '0 auto',\n        padding: '0 1.5rem',\n        position: 'relative',\n        zIndex: 10,\n        boxSizing: 'border-box'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"inline-flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center animate-glow\",\n            children: /*#__PURE__*/_jsxDEV(Zap, {\n              className: \"text-black\",\n              size: 28\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-3xl font-bold font-orbitron tracking-wider text-cyan-400\",\n            children: \"PressureMax\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"glass-strong p-8 rounded-2xl border border-cyan-500/30 animate-fade-in-up\",\n        style: {\n          width: '100%',\n          maxWidth: '100%',\n          boxSizing: 'border-box'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold font-orbitron text-white mb-2\",\n            children: \"Start Your Free Trial\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"Join 2,500+ businesses growing with AI marketing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          style: {\n            width: '100%',\n            maxWidth: '100%',\n            boxSizing: 'border-box',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem',\n              width: '100%',\n              maxWidth: '100%',\n              boxSizing: 'border-box'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"First Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"absolute text-gray-500 pointer-events-none\",\n                  size: 18,\n                  style: {\n                    left: '16px',\n                    zIndex: 10\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"firstName\",\n                  value: formData.firstName,\n                  onChange: handleChange,\n                  className: \"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\",\n                  style: {\n                    paddingLeft: '44px',\n                    paddingRight: '16px',\n                    paddingTop: '16px',\n                    paddingBottom: '16px',\n                    fontSize: '16px',\n                    lineHeight: '24px',\n                    height: '56px'\n                  },\n                  placeholder: \"First name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"lastName\",\n                value: formData.lastName,\n                onChange: handleChange,\n                className: \"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\",\n                style: {\n                  paddingLeft: '16px',\n                  paddingRight: '16px',\n                  paddingTop: '16px',\n                  paddingBottom: '16px',\n                  fontSize: '16px',\n                  lineHeight: '24px',\n                  height: '56px'\n                },\n                placeholder: \"Last name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                className: \"absolute text-gray-500 pointer-events-none\",\n                size: 18,\n                style: {\n                  left: '16px',\n                  zIndex: 10\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: \"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\",\n                style: {\n                  paddingLeft: '44px',\n                  paddingRight: '16px',\n                  paddingTop: '16px',\n                  paddingBottom: '16px',\n                  fontSize: '16px',\n                  lineHeight: '24px',\n                  height: '56px'\n                },\n                placeholder: \"Enter your email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"absolute text-gray-500 pointer-events-none\",\n                size: 18,\n                style: {\n                  left: '16px',\n                  zIndex: 10\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                name: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                className: \"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\",\n                style: {\n                  paddingLeft: '44px',\n                  paddingRight: '44px',\n                  paddingTop: '16px',\n                  paddingBottom: '16px',\n                  fontSize: '16px',\n                  lineHeight: '24px',\n                  height: '56px'\n                },\n                placeholder: \"Create a password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowPassword(!showPassword),\n                className: \"absolute text-gray-600 hover:text-cyan-500 transition-colors\",\n                style: {\n                  right: '16px',\n                  zIndex: 10,\n                  width: '18px',\n                  height: '18px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 35\n                }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 58\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"absolute text-gray-500 pointer-events-none\",\n                size: 18,\n                style: {\n                  left: '16px',\n                  zIndex: 10\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showConfirmPassword ? 'text' : 'password',\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                className: \"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\",\n                style: {\n                  paddingLeft: '44px',\n                  paddingRight: '44px',\n                  paddingTop: '16px',\n                  paddingBottom: '16px',\n                  fontSize: '16px',\n                  lineHeight: '24px',\n                  height: '56px'\n                },\n                placeholder: \"Confirm your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                className: \"absolute text-gray-600 hover:text-cyan-500 transition-colors\",\n                style: {\n                  right: '16px',\n                  zIndex: 10,\n                  width: '18px',\n                  height: '18px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-400\",\n            children: [\"By creating an account, you agree to our\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/terms\",\n              className: \"text-cyan-400 hover:text-cyan-300\",\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/privacy\",\n              className: \"text-cyan-400 hover:text-cyan-300\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"w-full cta-button btn-3d py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed\",\n            style: {\n              fontSize: '18px',\n              padding: '1rem 2rem',\n              minHeight: '56px'\n            },\n            children: loading ? /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Creating Account...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Start Free Trial\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-cyan-400 hover:text-cyan-300 font-semibold transition-colors\",\n              children: \"Sign in\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-sm text-gray-500 hover:text-gray-400 transition-colors\",\n            children: \"\\u2190 Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n}\n_s(Signup, \"9uRWFIz5ABTM/Z7L+0KeQAYxN2s=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "Link", "useNavigate", "Eye", "Eye<PERSON>ff", "Mail", "Lock", "User", "ArrowRight", "Zap", "CheckCircle", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Signup", "_s", "formData", "setFormData", "firstName", "lastName", "email", "password", "confirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "signUp", "navigate", "handleChange", "e", "target", "name", "value", "validateForm", "length", "handleSubmit", "preventDefault", "first_name", "last_name", "full_name", "setTimeout", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "style", "left", "animationDelay", "max<PERSON><PERSON><PERSON>", "width", "margin", "padding", "position", "zIndex", "boxSizing", "to", "onSubmit", "display", "flexDirection", "gap", "gridTemplateColumns", "type", "onChange", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "fontSize", "lineHeight", "height", "placeholder", "required", "onClick", "right", "alignItems", "justifyContent", "disabled", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Signup.js"], "sourcesContent": ["import { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Eye, EyeOff, Mail, Lock, User, ArrowRight, Zap, CheckCircle } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nfunction Signup() {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState(false);\n  \n  const { signUp } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    if (!validateForm()) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      await signUp(formData.email, formData.password, {\n        first_name: formData.firstName,\n        last_name: formData.lastName,\n        full_name: `${formData.firstName} ${formData.lastName}`\n      });\n      setSuccess(true);\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 2000);\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center relative overflow-hidden\">\n        <div className=\"absolute inset-0 circuit-bg opacity-20\"></div>\n        <div className=\"auth-container relative z-10\">\n          <div className=\"glass-strong p-8 rounded-2xl border border-cyan-500/30 text-center animate-scale-in\">\n            <div className=\"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <CheckCircle className=\"text-green-400\" size={32} />\n            </div>\n            <h2 className=\"text-2xl font-bold font-orbitron text-white mb-4\">\n              Account Created Successfully!\n            </h2>\n            <p className=\"text-gray-400 mb-6\">\n              Welcome to PressureMax! Please check your email to verify your account.\n            </p>\n            <div className=\"animate-pulse text-cyan-400\">\n              Redirecting to dashboard...\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black flex items-center justify-center relative overflow-hidden py-8\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 circuit-bg opacity-20\"></div>\n      <div className=\"particles\">\n        <div className=\"particle\" style={{left: '10%', animationDelay: '0s'}}></div>\n        <div className=\"particle\" style={{left: '30%', animationDelay: '2s'}}></div>\n        <div className=\"particle\" style={{left: '50%', animationDelay: '4s'}}></div>\n        <div className=\"particle\" style={{left: '70%', animationDelay: '1s'}}></div>\n        <div className=\"particle\" style={{left: '90%', animationDelay: '3s'}}></div>\n      </div>\n\n      <div style={{\n        maxWidth: '500px',\n        width: '100%',\n        margin: '0 auto',\n        padding: '0 1.5rem',\n        position: 'relative',\n        zIndex: 10,\n        boxSizing: 'border-box'\n      }}>\n        {/* Logo */}\n        <div className=\"text-center mb-8\">\n          <Link to=\"/\" className=\"inline-flex items-center space-x-3\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center animate-glow\">\n              <Zap className=\"text-black\" size={28} />\n            </div>\n            <span className=\"text-3xl font-bold font-orbitron tracking-wider text-cyan-400\">\n              PressureMax\n            </span>\n          </Link>\n        </div>\n\n        {/* Signup Form */}\n        <div className=\"glass-strong p-8 rounded-2xl border border-cyan-500/30 animate-fade-in-up\" style={{width: '100%', maxWidth: '100%', boxSizing: 'border-box'}}>\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold font-orbitron text-white mb-2\">\n              Start Your Free Trial\n            </h1>\n            <p className=\"text-gray-400\">\n              Join 2,500+ businesses growing with AI marketing\n            </p>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"auth-form\" style={{\n            width: '100%',\n            maxWidth: '100%',\n            boxSizing: 'border-box',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          }}>\n            {/* Name Fields */}\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem',\n              width: '100%',\n              maxWidth: '100%',\n              boxSizing: 'border-box'\n            }}>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  First Name\n                </label>\n                <div className=\"relative flex items-center\">\n                  <User className=\"absolute text-gray-500 pointer-events-none\" size={18} style={{ left: '16px', zIndex: 10 }} />\n                  <input\n                    type=\"text\"\n                    name=\"firstName\"\n                    value={formData.firstName}\n                    onChange={handleChange}\n                    className=\"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\"\n                    style={{\n                      paddingLeft: '44px',\n                      paddingRight: '16px',\n                      paddingTop: '16px',\n                      paddingBottom: '16px',\n                      fontSize: '16px',\n                      lineHeight: '24px',\n                      height: '56px'\n                    }}\n                    placeholder=\"First name\"\n                    required\n                  />\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Last Name\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleChange}\n                  className=\"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\"\n                  style={{\n                    paddingLeft: '16px',\n                    paddingRight: '16px',\n                    paddingTop: '16px',\n                    paddingBottom: '16px',\n                    fontSize: '16px',\n                    lineHeight: '24px',\n                    height: '56px'\n                  }}\n                  placeholder=\"Last name\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Email Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Email Address\n              </label>\n              <div className=\"relative flex items-center\">\n                <Mail className=\"absolute text-gray-500 pointer-events-none\" size={18} style={{ left: '16px', zIndex: 10 }} />\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\"\n                  style={{\n                    paddingLeft: '44px',\n                    paddingRight: '16px',\n                    paddingTop: '16px',\n                    paddingBottom: '16px',\n                    fontSize: '16px',\n                    lineHeight: '24px',\n                    height: '56px'\n                  }}\n                  placeholder=\"Enter your email\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Password\n              </label>\n              <div className=\"relative flex items-center\">\n                <Lock className=\"absolute text-gray-500 pointer-events-none\" size={18} style={{ left: '16px', zIndex: 10 }} />\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\"\n                  style={{\n                    paddingLeft: '44px',\n                    paddingRight: '44px',\n                    paddingTop: '16px',\n                    paddingBottom: '16px',\n                    fontSize: '16px',\n                    lineHeight: '24px',\n                    height: '56px'\n                  }}\n                  placeholder=\"Create a password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute text-gray-600 hover:text-cyan-500 transition-colors\"\n                  style={{\n                    right: '16px',\n                    zIndex: 10,\n                    width: '18px',\n                    height: '18px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}\n                </button>\n              </div>\n            </div>\n\n            {/* Confirm Password Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Confirm Password\n              </label>\n              <div className=\"relative flex items-center\">\n                <Lock className=\"absolute text-gray-500 pointer-events-none\" size={18} style={{ left: '16px', zIndex: 10 }} />\n                <input\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  name=\"confirmPassword\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  className=\"w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors\"\n                  style={{\n                    paddingLeft: '44px',\n                    paddingRight: '44px',\n                    paddingTop: '16px',\n                    paddingBottom: '16px',\n                    fontSize: '16px',\n                    lineHeight: '24px',\n                    height: '56px'\n                  }}\n                  placeholder=\"Confirm your password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  className=\"absolute text-gray-600 hover:text-cyan-500 transition-colors\"\n                  style={{\n                    right: '16px',\n                    zIndex: 10,\n                    width: '18px',\n                    height: '18px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}\n                </button>\n              </div>\n            </div>\n\n            {/* Terms Agreement */}\n            <div className=\"text-sm text-gray-400\">\n              By creating an account, you agree to our{' '}\n              <Link to=\"/terms\" className=\"text-cyan-400 hover:text-cyan-300\">\n                Terms of Service\n              </Link>{' '}\n              and{' '}\n              <Link to=\"/privacy\" className=\"text-cyan-400 hover:text-cyan-300\">\n                Privacy Policy\n              </Link>\n            </div>\n\n            {/* Submit Button */}\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full cta-button btn-3d py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed\"\n              style={{\n                fontSize: '18px',\n                padding: '1rem 2rem',\n                minHeight: '56px'\n              }}\n            >\n              {loading ? (\n                <span>Creating Account...</span>\n              ) : (\n                <>\n                  <span>Start Free Trial</span>\n                  <ArrowRight size={20} />\n                </>\n              )}\n            </button>\n          </form>\n\n          {/* Login Link */}\n          <div className=\"mt-8 text-center\">\n            <p className=\"text-gray-400\">\n              Already have an account?{' '}\n              <Link \n                to=\"/login\" \n                className=\"text-cyan-400 hover:text-cyan-300 font-semibold transition-colors\"\n              >\n                Sign in\n              </Link>\n            </p>\n          </div>\n\n          {/* Back to Home */}\n          <div className=\"mt-6 text-center\">\n            <Link \n              to=\"/\" \n              className=\"text-sm text-gray-500 hover:text-gray-400 transition-colors\"\n            >\n              ← Back to Home\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Signup;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,GAAG,EAAEC,WAAW,QAAQ,cAAc;AAC1F,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEmC;EAAO,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC5B,MAAMyB,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1BnB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxB,QAAQ,CAACK,QAAQ,KAAKL,QAAQ,CAACM,eAAe,EAAE;MAClDQ,QAAQ,CAAC,wBAAwB,CAAC;MAClC,OAAO,KAAK;IACd;IACA,IAAId,QAAQ,CAACK,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;MAChCX,QAAQ,CAAC,6CAA6C,CAAC;MACvD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMY,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBf,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI,CAACU,YAAY,CAAC,CAAC,EAAE;MACnBZ,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMK,MAAM,CAACjB,QAAQ,CAACI,KAAK,EAAEJ,QAAQ,CAACK,QAAQ,EAAE;QAC9CuB,UAAU,EAAE5B,QAAQ,CAACE,SAAS;QAC9B2B,SAAS,EAAE7B,QAAQ,CAACG,QAAQ;QAC5B2B,SAAS,EAAE,GAAG9B,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACG,QAAQ;MACvD,CAAC,CAAC;MACFa,UAAU,CAAC,IAAI,CAAC;MAChBe,UAAU,CAAC,MAAM;QACfb,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACmB,OAAO,CAAC;IACzB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIG,OAAO,EAAE;IACX,oBACEpB,OAAA;MAAKsC,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC9FvC,OAAA;QAAKsC,SAAS,EAAC;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9D3C,OAAA;QAAKsC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CvC,OAAA;UAAKsC,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAClGvC,OAAA;YAAKsC,SAAS,EAAC,sFAAsF;YAAAC,QAAA,eACnGvC,OAAA,CAACH,WAAW;cAACyC,SAAS,EAAC,gBAAgB;cAACM,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACN3C,OAAA;YAAIsC,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YAAGsC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3C,OAAA;YAAKsC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAE7C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3C,OAAA;IAAKsC,SAAS,EAAC,sFAAsF;IAAAC,QAAA,gBAEnGvC,OAAA;MAAKsC,SAAS,EAAC;IAAwC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC9D3C,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvC,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAACC,IAAI,EAAE,KAAK;UAAEC,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5E3C,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAACC,IAAI,EAAE,KAAK;UAAEC,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5E3C,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAACC,IAAI,EAAE,KAAK;UAAEC,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5E3C,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAACC,IAAI,EAAE,KAAK;UAAEC,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5E3C,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAACC,IAAI,EAAE,KAAK;UAAEC,cAAc,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAEN3C,OAAA;MAAK6C,KAAK,EAAE;QACVG,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,UAAU;QACnBC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,gBAEAvC,OAAA;QAAKsC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BvC,OAAA,CAACZ,IAAI;UAACmE,EAAE,EAAC,GAAG;UAACjB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACzDvC,OAAA;YAAKsC,SAAS,EAAC,gHAAgH;YAAAC,QAAA,eAC7HvC,OAAA,CAACJ,GAAG;cAAC0C,SAAS,EAAC,YAAY;cAACM,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN3C,OAAA;YAAMsC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3C,OAAA;QAAKsC,SAAS,EAAC,2EAA2E;QAACO,KAAK,EAAE;UAACI,KAAK,EAAE,MAAM;UAAED,QAAQ,EAAE,MAAM;UAAEM,SAAS,EAAE;QAAY,CAAE;QAAAf,QAAA,gBAC3JvC,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvC,OAAA;YAAIsC,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YAAGsC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAELzB,KAAK,iBACJlB,OAAA;UAAKsC,SAAS,EAAC,+EAA+E;UAAAC,QAAA,EAC3FrB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED3C,OAAA;UAAMwD,QAAQ,EAAEzB,YAAa;UAACO,SAAS,EAAC,WAAW;UAACO,KAAK,EAAE;YACzDI,KAAK,EAAE,MAAM;YACbD,QAAQ,EAAE,MAAM;YAChBM,SAAS,EAAE,YAAY;YACvBG,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,GAAG,EAAE;UACP,CAAE;UAAApB,QAAA,gBAEAvC,OAAA;YAAK6C,KAAK,EAAE;cACVY,OAAO,EAAE,MAAM;cACfG,mBAAmB,EAAE,SAAS;cAC9BD,GAAG,EAAE,MAAM;cACXV,KAAK,EAAE,MAAM;cACbD,QAAQ,EAAE,MAAM;cAChBM,SAAS,EAAE;YACb,CAAE;YAAAf,QAAA,gBACAvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3C,OAAA;gBAAKsC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCvC,OAAA,CAACN,IAAI;kBAAC4C,SAAS,EAAC,4CAA4C;kBAACM,IAAI,EAAE,EAAG;kBAACC,KAAK,EAAE;oBAAEC,IAAI,EAAE,MAAM;oBAAEO,MAAM,EAAE;kBAAG;gBAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9G3C,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACXlC,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEvB,QAAQ,CAACE,SAAU;kBAC1BuD,QAAQ,EAAEtC,YAAa;kBACvBc,SAAS,EAAC,4JAA4J;kBACtKO,KAAK,EAAE;oBACLkB,WAAW,EAAE,MAAM;oBACnBC,YAAY,EAAE,MAAM;oBACpBC,UAAU,EAAE,MAAM;oBAClBC,aAAa,EAAE,MAAM;oBACrBC,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE;kBACV,CAAE;kBACFC,WAAW,EAAC,YAAY;kBACxBC,QAAQ;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3C,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACXlC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEvB,QAAQ,CAACG,QAAS;gBACzBsD,QAAQ,EAAEtC,YAAa;gBACvBc,SAAS,EAAC,4JAA4J;gBACtKO,KAAK,EAAE;kBACLkB,WAAW,EAAE,MAAM;kBACnBC,YAAY,EAAE,MAAM;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,MAAM;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACFC,WAAW,EAAC,WAAW;gBACvBC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3C,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAOsC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA;cAAKsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvC,OAAA,CAACR,IAAI;gBAAC8C,SAAS,EAAC,4CAA4C;gBAACM,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAE;kBAAEC,IAAI,EAAE,MAAM;kBAAEO,MAAM,EAAE;gBAAG;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9G3C,OAAA;gBACE6D,IAAI,EAAC,OAAO;gBACZlC,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEvB,QAAQ,CAACI,KAAM;gBACtBqD,QAAQ,EAAEtC,YAAa;gBACvBc,SAAS,EAAC,4JAA4J;gBACtKO,KAAK,EAAE;kBACLkB,WAAW,EAAE,MAAM;kBACnBC,YAAY,EAAE,MAAM;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,MAAM;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACFC,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3C,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAOsC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA;cAAKsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvC,OAAA,CAACP,IAAI;gBAAC6C,SAAS,EAAC,4CAA4C;gBAACM,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAE;kBAAEC,IAAI,EAAE,MAAM;kBAAEO,MAAM,EAAE;gBAAG;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9G3C,OAAA;gBACE6D,IAAI,EAAEjD,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCe,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEvB,QAAQ,CAACK,QAAS;gBACzBoD,QAAQ,EAAEtC,YAAa;gBACvBc,SAAS,EAAC,4JAA4J;gBACtKO,KAAK,EAAE;kBACLkB,WAAW,EAAE,MAAM;kBACnBC,YAAY,EAAE,MAAM;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,MAAM;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACFC,WAAW,EAAC,mBAAmB;gBAC/BC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF3C,OAAA;gBACE6D,IAAI,EAAC,QAAQ;gBACbW,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9C0B,SAAS,EAAC,8DAA8D;gBACxEO,KAAK,EAAE;kBACL4B,KAAK,EAAE,MAAM;kBACbpB,MAAM,EAAE,EAAE;kBACVJ,KAAK,EAAE,MAAM;kBACboB,MAAM,EAAE,MAAM;kBACdZ,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAApC,QAAA,EAED3B,YAAY,gBAAGZ,OAAA,CAACT,MAAM;kBAACqD,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3C,OAAA,CAACV,GAAG;kBAACsD,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3C,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAOsC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA;cAAKsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvC,OAAA,CAACP,IAAI;gBAAC6C,SAAS,EAAC,4CAA4C;gBAACM,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAE;kBAAEC,IAAI,EAAE,MAAM;kBAAEO,MAAM,EAAE;gBAAG;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9G3C,OAAA;gBACE6D,IAAI,EAAE/C,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChDa,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEvB,QAAQ,CAACM,eAAgB;gBAChCmD,QAAQ,EAAEtC,YAAa;gBACvBc,SAAS,EAAC,4JAA4J;gBACtKO,KAAK,EAAE;kBACLkB,WAAW,EAAE,MAAM;kBACnBC,YAAY,EAAE,MAAM;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,aAAa,EAAE,MAAM;kBACrBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,MAAM;kBAClBC,MAAM,EAAE;gBACV,CAAE;gBACFC,WAAW,EAAC,uBAAuB;gBACnCC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF3C,OAAA;gBACE6D,IAAI,EAAC,QAAQ;gBACbW,OAAO,EAAEA,CAAA,KAAMzD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAC5DwB,SAAS,EAAC,8DAA8D;gBACxEO,KAAK,EAAE;kBACL4B,KAAK,EAAE,MAAM;kBACbpB,MAAM,EAAE,EAAE;kBACVJ,KAAK,EAAE,MAAM;kBACboB,MAAM,EAAE,MAAM;kBACdZ,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAApC,QAAA,EAEDzB,mBAAmB,gBAAGd,OAAA,CAACT,MAAM;kBAACqD,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3C,OAAA,CAACV,GAAG;kBAACsD,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3C,OAAA;YAAKsC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,0CACG,EAAC,GAAG,eAC5CvC,OAAA,CAACZ,IAAI;cAACmE,EAAE,EAAC,QAAQ;cAACjB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACP3C,OAAA,CAACZ,IAAI;cAACmE,EAAE,EAAC,UAAU;cAACjB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN3C,OAAA;YACE6D,IAAI,EAAC,QAAQ;YACbe,QAAQ,EAAE5D,OAAQ;YAClBsB,SAAS,EAAC,qGAAqG;YAC/GO,KAAK,EAAE;cACLsB,QAAQ,EAAE,MAAM;cAChBhB,OAAO,EAAE,WAAW;cACpB0B,SAAS,EAAE;YACb,CAAE;YAAAtC,QAAA,EAEDvB,OAAO,gBACNhB,OAAA;cAAAuC,QAAA,EAAM;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEhC3C,OAAA,CAAAE,SAAA;cAAAqC,QAAA,gBACEvC,OAAA;gBAAAuC,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7B3C,OAAA,CAACL,UAAU;gBAACiD,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACxB;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGP3C,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvC,OAAA;YAAGsC,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,0BACH,EAAC,GAAG,eAC5BvC,OAAA,CAACZ,IAAI;cACHmE,EAAE,EAAC,QAAQ;cACXjB,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC9E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN3C,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvC,OAAA,CAACZ,IAAI;YACHmE,EAAE,EAAC,GAAG;YACNjB,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvC,EAAA,CA7XQD,MAAM;EAAA,QAcML,OAAO,EACTT,WAAW;AAAA;AAAAyF,EAAA,GAfrB3E,MAAM;AA+Xf,eAAeA,MAAM;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
@echo off
echo ========================================
echo    PRESSURE MAX - STOP ALL SERVICES
echo ========================================

echo 🛑 Stopping all Node.js processes...

:: Kill all node processes (be careful with this!)
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im nodemon.exe >nul 2>&1

:: Kill specific processes by window title if possible
taskkill /fi "WindowTitle eq Pressure Max API*" /f >nul 2>&1
taskkill /fi "WindowTitle eq Pressure Max Frontend*" /f >nul 2>&1
taskkill /fi "WindowTitle eq API Server*" /f >nul 2>&1
taskkill /fi "WindowTitle eq Frontend*" /f >nul 2>&1

echo ✅ All services stopped

:: Check if any node processes are still running
echo.
echo 🔍 Checking for remaining Node.js processes...
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if %errorlevel% equ 0 (
    echo ⚠️  Some Node.js processes may still be running
    echo    You may need to close them manually
) else (
    echo ✅ No Node.js processes found
)

echo.
echo 📊 Port status:
echo Checking port 3000 (Backend API)...
netstat -an | findstr ":3000 " 2>nul
echo Checking port 3001 (Frontend)...
netstat -an | findstr ":3001 " 2>nul

echo.
echo Press any key to exit...
pause >nul

[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js": "16", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js": "17", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js": "18", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js": "19", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Testing.js": "20"}, {"size": 232, "mtime": 1750870627179, "results": "21", "hashOfConfig": "22"}, {"size": 1835, "mtime": 1750998212651, "results": "23", "hashOfConfig": "22"}, {"size": 11019, "mtime": 1750998922321, "results": "24", "hashOfConfig": "22"}, {"size": 18514, "mtime": 1750938756494, "results": "25", "hashOfConfig": "22"}, {"size": 7324, "mtime": 1750904956983, "results": "26", "hashOfConfig": "22"}, {"size": 8243, "mtime": 1750873838649, "results": "27", "hashOfConfig": "22"}, {"size": 4969, "mtime": 1751001959609, "results": "28", "hashOfConfig": "22"}, {"size": 4416, "mtime": 1750938756489, "results": "29", "hashOfConfig": "22"}, {"size": 4531, "mtime": 1750906642416, "results": "30", "hashOfConfig": "22"}, {"size": 4881, "mtime": 1750906513515, "results": "31", "hashOfConfig": "22"}, {"size": 15521, "mtime": 1750913282515, "results": "32", "hashOfConfig": "22"}, {"size": 11191, "mtime": 1750915070114, "results": "33", "hashOfConfig": "22"}, {"size": 28034, "mtime": 1750938756493, "results": "34", "hashOfConfig": "22"}, {"size": 21576, "mtime": 1750938756324, "results": "35", "hashOfConfig": "22"}, {"size": 6039, "mtime": 1750999559481, "results": "36", "hashOfConfig": "22"}, {"size": 32494, "mtime": 1751034929652, "results": "37", "hashOfConfig": "22"}, {"size": 526, "mtime": 1750992184196, "results": "38", "hashOfConfig": "22"}, {"size": 8234, "mtime": 1751000268112, "results": "39", "hashOfConfig": "22"}, {"size": 14523, "mtime": 1750994562197, "results": "40", "hashOfConfig": "22"}, {"size": 3048, "mtime": 1750998108215, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["102", "103"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["104", "105"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["106", "107"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["108"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["109"], ["110", "111"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["112", "113"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js", ["114"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Testing.js", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 65, "column": 6, "nodeType": "117", "endLine": 65, "endColumn": 23, "suggestions": "118"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 76, "column": 6, "nodeType": "117", "endLine": 76, "endColumn": 46, "suggestions": "119"}, {"ruleId": "120", "severity": 1, "message": "121", "line": 8, "column": 28, "nodeType": "122", "messageId": "123", "endLine": 8, "endColumn": 32}, {"ruleId": "115", "severity": 1, "message": "124", "line": 22, "column": 6, "nodeType": "117", "endLine": 22, "endColumn": 23, "suggestions": "125"}, {"ruleId": "120", "severity": 1, "message": "126", "line": 5, "column": 20, "nodeType": "122", "messageId": "123", "endLine": 5, "endColumn": 25}, {"ruleId": "115", "severity": 1, "message": "127", "line": 25, "column": 6, "nodeType": "117", "endLine": 25, "endColumn": 32, "suggestions": "128"}, {"ruleId": "115", "severity": 1, "message": "129", "line": 24, "column": 6, "nodeType": "117", "endLine": 24, "endColumn": 8, "suggestions": "130"}, {"ruleId": "120", "severity": 1, "message": "131", "line": 23, "column": 10, "nodeType": "122", "messageId": "123", "endLine": 23, "endColumn": 27}, {"ruleId": "115", "severity": 1, "message": "132", "line": 87, "column": 6, "nodeType": "117", "endLine": 87, "endColumn": 8, "suggestions": "133", "suppressions": "134"}, {"ruleId": "115", "severity": 1, "message": "135", "line": 94, "column": 6, "nodeType": "117", "endLine": 94, "endColumn": 23, "suggestions": "136", "suppressions": "137"}, {"ruleId": "115", "severity": 1, "message": "127", "line": 108, "column": 6, "nodeType": "117", "endLine": 108, "endColumn": 8, "suggestions": "138", "suppressions": "139"}, {"ruleId": "115", "severity": 1, "message": "135", "line": 115, "column": 6, "nodeType": "117", "endLine": 115, "endColumn": 23, "suggestions": "140", "suppressions": "141"}, {"ruleId": "120", "severity": 1, "message": "142", "line": 14, "column": 9, "nodeType": "122", "messageId": "123", "endLine": 14, "endColumn": 17}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], ["144"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["145"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["146"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["147"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["148"], ["149"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["150"], ["151"], ["152"], ["153"], ["154"], ["155"], "'navigate' is assigned a value but never used.", {"desc": "156", "fix": "157"}, {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, {"kind": "168", "justification": "169"}, {"desc": "170", "fix": "171"}, {"kind": "168", "justification": "169"}, {"desc": "172", "fix": "173"}, {"kind": "168", "justification": "169"}, {"desc": "170", "fix": "174"}, {"kind": "168", "justification": "169"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [loadFormData]", {"range": "185", "text": "186"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "189", "text": "190"}, {"range": "191", "text": "188"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136]]
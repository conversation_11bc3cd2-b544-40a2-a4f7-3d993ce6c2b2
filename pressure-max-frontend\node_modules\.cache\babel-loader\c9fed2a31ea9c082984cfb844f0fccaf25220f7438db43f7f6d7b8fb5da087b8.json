{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useFacebook } from '../contexts/FacebookContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home, Settings, TrendingUp, Users, DollarSign, Eye, TestTube } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const {\n    isConnected: facebookConnected\n  } = useFacebook();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalCampaigns: 0,\n    activeAds: 0,\n    totalSpend: 0,\n    totalLeads: 0\n  });\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n  const handleGoToTesting = () => {\n    navigate('/testing');\n  };\n  useEffect(() => {\n    // Load dashboard stats\n    // This would typically fetch from your API\n    setStats({\n      totalCampaigns: 12,\n      activeAds: 8,\n      totalSpend: 2450.75,\n      totalLeads: 156\n    });\n  }, []);\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDCCA Pressure Max Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Monitor your Facebook marketing campaigns and performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-user-section\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Welcome, \", user.firstName || user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoToTesting,\n              className: \"nav-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), \"API Testing\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoToLanding,\n              className: \"nav-btn\",\n              children: [/*#__PURE__*/_jsxDEV(Home, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), \"Landing\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"logout-btn\",\n              children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"connection-status\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-card ${facebookConnected ? 'connected' : 'disconnected'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-icon\",\n            children: facebookConnected ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Facebook Connection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: facebookConnected ? 'Connected and ready' : 'Not connected'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), !facebookConnected && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoToTesting,\n            className: \"btn btn-primary\",\n            children: \"Connect Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Total Campaigns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-number\",\n              children: stats.totalCampaigns\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: /*#__PURE__*/_jsxDEV(Eye, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Active Ads\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-number\",\n              children: stats.activeAds\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: /*#__PURE__*/_jsxDEV(DollarSign, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Total Spend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-number\",\n              children: [\"$\", stats.totalSpend.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: /*#__PURE__*/_jsxDEV(Users, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Total Leads\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-number\",\n              children: stats.totalLeads\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"actions-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoToTesting,\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(TestTube, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"API Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Test and debug API endpoints\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoToTesting,\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Create Campaign\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Launch a new Facebook campaign\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoToTesting,\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(Users, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Manage Lead Forms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Create and edit lead forms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoToTesting,\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Account Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Manage your account preferences\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"dashboard-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Pressure Max API Testing Interface - Built with React\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"http://localhost:3000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"u71PATsgeCIY9grbCYtDAjyz+v8=\", false, function () {\n  return [useAuth, useFacebook, useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useFacebook", "useNavigate", "LogOut", "User", "Home", "Settings", "TrendingUp", "Users", "DollarSign", "Eye", "TestTube", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "isAuthenticated", "logout", "isConnected", "facebookConnected", "navigate", "stats", "setStats", "totalCampaigns", "activeAds", "totalSpend", "totalLeads", "handleLogout", "handleGoToLanding", "handleGoToTesting", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "email", "onClick", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useFacebook } from '../contexts/FacebookContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home, Settings, TrendingUp, Users, DollarSign, Eye, TestTube } from 'lucide-react';\n\nconst Dashboard = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const { isConnected: facebookConnected } = useFacebook();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalCampaigns: 0,\n    activeAds: 0,\n    totalSpend: 0,\n    totalLeads: 0\n  });\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n\n  const handleGoToTesting = () => {\n    navigate('/testing');\n  };\n\n  useEffect(() => {\n    // Load dashboard stats\n    // This would typically fetch from your API\n    setStats({\n      totalCampaigns: 12,\n      activeAds: 8,\n      totalSpend: 2450.75,\n      totalLeads: 156\n    });\n  }, []);\n\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n\n  return (\n    <div className=\"dashboard\">\n      {/* Dashboard Header */}\n      <header className=\"dashboard-header\">\n        <div className=\"dashboard-header-content\">\n          <div className=\"dashboard-title\">\n            <h1>📊 Pressure Max Dashboard</h1>\n            <p>Monitor your Facebook marketing campaigns and performance</p>\n          </div>\n\n          <div className=\"dashboard-user-section\">\n            {user && (\n              <div className=\"user-info\">\n                <User size={16} />\n                <span>Welcome, {user.firstName || user.email}</span>\n              </div>\n            )}\n\n            <div className=\"dashboard-actions\">\n              <button onClick={handleGoToTesting} className=\"nav-btn\">\n                <TestTube size={16} />\n                API Testing\n              </button>\n              <button onClick={handleGoToLanding} className=\"nav-btn\">\n                <Home size={16} />\n                Landing\n              </button>\n              <button onClick={handleLogout} className=\"logout-btn\">\n                <LogOut size={16} />\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Dashboard Main Content */}\n      <main className=\"dashboard-main\">\n        {/* Connection Status */}\n        <div className=\"connection-status\">\n          <div className={`status-card ${facebookConnected ? 'connected' : 'disconnected'}`}>\n            <div className=\"status-icon\">\n              {facebookConnected ? '✅' : '❌'}\n            </div>\n            <div className=\"status-info\">\n              <h3>Facebook Connection</h3>\n              <p>{facebookConnected ? 'Connected and ready' : 'Not connected'}</p>\n            </div>\n            {!facebookConnected && (\n              <button onClick={handleGoToTesting} className=\"btn btn-primary\">\n                Connect Facebook\n              </button>\n            )}\n          </div>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"stats-grid\">\n          <div className=\"stat-card\">\n            <div className=\"stat-icon\">\n              <TrendingUp size={24} />\n            </div>\n            <div className=\"stat-content\">\n              <h3>Total Campaigns</h3>\n              <p className=\"stat-number\">{stats.totalCampaigns}</p>\n            </div>\n          </div>\n\n          <div className=\"stat-card\">\n            <div className=\"stat-icon\">\n              <Eye size={24} />\n            </div>\n            <div className=\"stat-content\">\n              <h3>Active Ads</h3>\n              <p className=\"stat-number\">{stats.activeAds}</p>\n            </div>\n          </div>\n\n          <div className=\"stat-card\">\n            <div className=\"stat-icon\">\n              <DollarSign size={24} />\n            </div>\n            <div className=\"stat-content\">\n              <h3>Total Spend</h3>\n              <p className=\"stat-number\">${stats.totalSpend.toFixed(2)}</p>\n            </div>\n          </div>\n\n          <div className=\"stat-card\">\n            <div className=\"stat-icon\">\n              <Users size={24} />\n            </div>\n            <div className=\"stat-content\">\n              <h3>Total Leads</h3>\n              <p className=\"stat-number\">{stats.totalLeads}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"quick-actions\">\n          <h2>Quick Actions</h2>\n          <div className=\"actions-grid\">\n            <button onClick={handleGoToTesting} className=\"action-card\">\n              <TestTube size={32} />\n              <h3>API Testing</h3>\n              <p>Test and debug API endpoints</p>\n            </button>\n\n            <button onClick={handleGoToTesting} className=\"action-card\">\n              <TrendingUp size={32} />\n              <h3>Create Campaign</h3>\n              <p>Launch a new Facebook campaign</p>\n            </button>\n\n            <button onClick={handleGoToTesting} className=\"action-card\">\n              <Users size={32} />\n              <h3>Manage Lead Forms</h3>\n              <p>Create and edit lead forms</p>\n            </button>\n\n            <button onClick={handleGoToTesting} className=\"action-card\">\n              <Settings size={32} />\n              <h3>Account Settings</h3>\n              <p>Manage your account preferences</p>\n            </button>\n          </div>\n        </div>\n      </main>\n\n      {/* Dashboard Footer */}\n      <footer className=\"dashboard-footer\">\n        <p>Pressure Max API Testing Interface - Built with React</p>\n        <p>Backend API: <code>http://localhost:3000</code></p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1G,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEmB,WAAW,EAAEC;EAAkB,CAAC,GAAGnB,WAAW,CAAC,CAAC;EACxD,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC;IACjC0B,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMV,MAAM,CAAC,CAAC;IACdG,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9BT,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACd;IACA;IACAwB,QAAQ,CAAC;MACPC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACV,eAAe,EAAE;IACpBI,QAAQ,CAAC,UAAU,CAAC;IACpB,OAAO,IAAI;EACb;EAEA,oBACER,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnB,OAAA;MAAQkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAClCnB,OAAA;QAAKkB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCnB,OAAA;UAAKkB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnB,OAAA;YAAAmB,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCvB,OAAA;YAAAmB,QAAA,EAAG;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpChB,IAAI,iBACHH,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnB,OAAA,CAACT,IAAI;cAACiC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClBvB,OAAA;cAAAmB,QAAA,GAAM,WAAS,EAAChB,IAAI,CAACsB,SAAS,IAAItB,IAAI,CAACuB,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN,eAEDvB,OAAA;YAAKkB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCnB,OAAA;cAAQ2B,OAAO,EAAEV,iBAAkB;cAACC,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACrDnB,OAAA,CAACF,QAAQ;gBAAC0B,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cAAQ2B,OAAO,EAAEX,iBAAkB;cAACE,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACrDnB,OAAA,CAACR,IAAI;gBAACgC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cAAQ2B,OAAO,EAAEZ,YAAa;cAACG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACnDnB,OAAA,CAACV,MAAM;gBAACkC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTvB,OAAA;MAAMkB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE9BnB,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCnB,OAAA;UAAKkB,SAAS,EAAE,eAAeX,iBAAiB,GAAG,WAAW,GAAG,cAAc,EAAG;UAAAY,QAAA,gBAChFnB,OAAA;YAAKkB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBZ,iBAAiB,GAAG,GAAG,GAAG;UAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnB,OAAA;cAAAmB,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BvB,OAAA;cAAAmB,QAAA,EAAIZ,iBAAiB,GAAG,qBAAqB,GAAG;YAAe;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,EACL,CAAChB,iBAAiB,iBACjBP,OAAA;YAAQ2B,OAAO,EAAEV,iBAAkB;YAACC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBnB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnB,OAAA,CAACN,UAAU;cAAC8B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnB,OAAA;cAAAmB,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBvB,OAAA;cAAGkB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEV,KAAK,CAACE;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnB,OAAA,CAACH,GAAG;cAAC2B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnB,OAAA;cAAAmB,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBvB,OAAA;cAAGkB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEV,KAAK,CAACG;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnB,OAAA,CAACJ,UAAU;cAAC4B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnB,OAAA;cAAAmB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBvB,OAAA;cAAGkB,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,GAAC,EAACV,KAAK,CAACI,UAAU,CAACe,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnB,OAAA,CAACL,KAAK;cAAC6B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACNvB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnB,OAAA;cAAAmB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBvB,OAAA;cAAGkB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEV,KAAK,CAACK;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnB,OAAA;UAAAmB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBvB,OAAA;UAAKkB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnB,OAAA;YAAQ2B,OAAO,EAAEV,iBAAkB;YAACC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACzDnB,OAAA,CAACF,QAAQ;cAAC0B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtBvB,OAAA;cAAAmB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBvB,OAAA;cAAAmB,QAAA,EAAG;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAETvB,OAAA;YAAQ2B,OAAO,EAAEV,iBAAkB;YAACC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACzDnB,OAAA,CAACN,UAAU;cAAC8B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBvB,OAAA;cAAAmB,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBvB,OAAA;cAAAmB,QAAA,EAAG;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eAETvB,OAAA;YAAQ2B,OAAO,EAAEV,iBAAkB;YAACC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACzDnB,OAAA,CAACL,KAAK;cAAC6B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnBvB,OAAA;cAAAmB,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BvB,OAAA;cAAAmB,QAAA,EAAG;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAETvB,OAAA;YAAQ2B,OAAO,EAAEV,iBAAkB;YAACC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACzDnB,OAAA,CAACP,QAAQ;cAAC+B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtBvB,OAAA;cAAAmB,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBvB,OAAA;cAAAmB,QAAA,EAAG;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvB,OAAA;MAAQkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAClCnB,OAAA;QAAAmB,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5DvB,OAAA;QAAAmB,QAAA,GAAG,eAAa,eAAAnB,OAAA;UAAAmB,QAAA,EAAM;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrB,EAAA,CAjLID,SAAS;EAAA,QAC6Bd,OAAO,EACNC,WAAW,EACrCC,WAAW;AAAA;AAAAwC,EAAA,GAHxB5B,SAAS;AAmLf,eAAeA,SAAS;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": "0.2", "language": "en", "words": ["adset", "adsets", "leadforms", "leadgen", "leadform", "ROAS", "THRUPLAY", "facebook", "api", "campaigns", "insights", "targeting", "creatives", "audiences", "placements", "optimization", "bidding", "impressions", "conversions", "attribution", "lookalike", "retargeting", "demographics", "psychographics", "geotargeting", "dayparting", "frequency", "reach", "ctr", "cpc", "cpm", "cpp", "cpa", "roas", "roi", "orbitron", "Supabase", "SUPABASE", "XVCJ", "VAPI", "vapi", "whsec", "OPENROUTER"], "ignorePaths": ["node_modules/**", ".git/**", "dist/**", "build/**"]}
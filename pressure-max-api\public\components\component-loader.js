/**
 * Component Loader System
 * Dynamically loads and injects reusable components into pages
 */

class ComponentLoader {
    constructor() {
        this.components = new Map();
        this.loadedComponents = new Set();
        this.componentCache = this.loadCacheFromStorage();
        this.isInitialized = false;
    }

    /**
     * Load component cache from localStorage
     */
    loadCacheFromStorage() {
        try {
            const cached = localStorage.getItem('pressure_max_components');
            if (cached) {
                const parsed = JSON.parse(cached);
                console.log('📦 Loaded component cache from storage');
                return new Map(Object.entries(parsed));
            }
        } catch (error) {
            console.log('Could not load component cache:', error);
        }
        return new Map();
    }

    /**
     * Save component cache to localStorage
     */
    saveCacheToStorage() {
        try {
            const cacheObj = Object.fromEntries(this.componentCache);
            localStorage.setItem('pressure_max_components', JSON.stringify(cacheObj));
        } catch (error) {
            console.log('Could not save component cache:', error);
        }
    }

    /**
     * Clear component cache (useful for development)
     */
    clearCache() {
        this.components.clear();
        this.componentCache.clear();
        localStorage.removeItem('pressure_max_components');
        console.log('🗑️ Component cache cleared');
    }

    /**
     * Load a component from cache or file
     */
    async loadComponent(componentName) {
        // Check memory cache first
        if (this.components.has(componentName)) {
            return this.components.get(componentName);
        }

        // Check persistent cache
        if (this.componentCache.has(componentName)) {
            const html = this.componentCache.get(componentName);
            this.components.set(componentName, html);
            console.log(`💾 Loaded ${componentName} from cache`);
            return html;
        }

        try {
            const response = await fetch(`/components/${componentName}.html`);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${componentName}`);
            }

            const html = await response.text();

            // Store in both memory and persistent cache
            this.components.set(componentName, html);
            this.componentCache.set(componentName, html);
            this.saveCacheToStorage();

            console.log(`📥 Loaded ${componentName} from server and cached`);
            return html;
        } catch (error) {
            console.error(`Error loading component ${componentName}:`, error);
            return null;
        }
    }

    /**
     * Inject a component into a target element
     */
    async injectComponent(componentName, targetSelector) {
        const target = document.querySelector(targetSelector);
        if (!target) {
            console.error(`Target element not found: ${targetSelector}`);
            return false;
        }

        // Add loading state
        target.classList.add('loading');

        const componentHtml = await this.loadComponent(componentName);
        if (!componentHtml) {
            console.error(`Failed to load component: ${componentName}`);
            target.classList.remove('loading');
            return false;
        }

        // Inject component and remove loading state
        target.innerHTML = componentHtml;
        target.classList.remove('loading');
        this.loadedComponents.add(componentName);

        // Re-initialize Lucide icons for the new component
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        return true;
    }

    /**
     * Load multiple components
     */
    async loadComponents(components) {
        const promises = components.map(({ name, target }) => 
            this.injectComponent(name, target)
        );
        
        const results = await Promise.all(promises);
        return results.every(result => result === true);
    }

    /**
     * Set active navigation item based on current page
     */
    setActiveNavItem(pageId) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current page nav item
        const currentNavItem = document.querySelector(`[data-page="${pageId}"]`);
        if (currentNavItem) {
            currentNavItem.classList.add('active');
        }
    }

    /**
     * Initialize component system for a page
     */
    async initializePage(pageId, customComponents = []) {
        // Mark as loading to show subtle loading state
        document.body.classList.add('components-loading');

        // Default components that every page should have
        const defaultComponents = [
            { name: 'sidebar', target: '#sidebar-container' },
            { name: 'header', target: '#header-container' }
        ];

        // Combine default and custom components
        const allComponents = [...defaultComponents, ...customComponents];

        // Try to inject cached components immediately to prevent flash
        await this.injectCachedComponentsImmediately(allComponents);

        // Check if components are already loaded (for smooth navigation)
        const needsLoading = allComponents.some(({ target }) => {
            const element = document.querySelector(target);
            return !element || element.innerHTML.trim() === '';
        });

        if (needsLoading) {
            // Load all components in parallel for faster loading
            const success = await this.loadComponents(allComponents);

            if (!success) {
                console.error(`Failed to initialize page ${pageId}`);
                // Don't return false - still show the page
            }
        }

        // Set active navigation (always do this)
        this.setActiveNavItem(pageId);

        // Initialize component functionality (always do this)
        this.initializeComponentFunctionality();

        // Ensure components are visible
        allComponents.forEach(({ target }) => {
            const element = document.querySelector(target);
            if (element) {
                element.style.opacity = '1';
                element.classList.remove('loading');
            }
        });

        // Mark components as loaded and remove loading state
        document.body.classList.remove('components-loading');
        document.body.classList.add('components-loaded');

        // Also mark main content as ready
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('components-ready');
        }

        console.log(`Page ${pageId} initialized successfully`);
        return true;
    }

    /**
     * Inject cached components immediately to prevent flash
     */
    async injectCachedComponentsImmediately(components) {
        for (const { name, target } of components) {
            const element = document.querySelector(target);
            if (!element || element.innerHTML.trim() !== '') continue;

            // Try to get from cache
            let html = this.components.get(name) || this.componentCache.get(name);

            if (html) {
                element.innerHTML = html;
                element.classList.remove('loading');
                console.log(`⚡ Instantly injected cached ${name}`);

                // Re-initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        }
    }

    /**
     * Initialize functionality for loaded components
     */
    initializeComponentFunctionality() {
        // Mobile menu toggle
        const menuToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
            });
            
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 1024) {
                    if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                        sidebar.classList.remove('open');
                    }
                }
            });
        }

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                console.log('Searching for:', searchTerm);
                // Add search logic here
            });
        }

        // Navigation active state
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Remove active class from all nav items
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // Add active class to clicked item
                this.closest('.nav-item').classList.add('active');
            });
        });

        // Header buttons
        const headerBtns = document.querySelectorAll('.header-btn:not(.logout-btn)');
        headerBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i').getAttribute('data-lucide');
                console.log('Header button clicked:', icon);
                
                if (icon === 'bell') {
                    showNotification('No new notifications', 'info');
                } else if (icon === 'settings') {
                    showNotification('Settings panel coming soon!', 'info');
                }
            });
        });

        // New project button
        const newProjectBtn = document.querySelector('.new-project-btn');
        if (newProjectBtn) {
            newProjectBtn.addEventListener('click', function() {
                console.log('New project button clicked');
                showNotification('New project feature coming soon!', 'info');
            });
        }
    }
}

// Create global instance
window.componentLoader = new ComponentLoader();

// Immediate component injection to prevent flash
(function immediateComponentInjection() {
    const componentLoader = new ComponentLoader();
    window.componentLoader = componentLoader;

    // Try to inject cached components immediately
    const sidebarContainer = document.querySelector('#sidebar-container');
    const headerContainer = document.querySelector('#header-container');
    let componentsInjected = 0;

    if (sidebarContainer && componentLoader.componentCache.has('sidebar')) {
        sidebarContainer.innerHTML = componentLoader.componentCache.get('sidebar');
        sidebarContainer.classList.remove('loading');
        console.log('⚡ Immediately injected cached sidebar');
        componentsInjected++;
    }

    if (headerContainer && componentLoader.componentCache.has('header')) {
        headerContainer.innerHTML = componentLoader.componentCache.get('header');
        headerContainer.classList.remove('loading');
        console.log('⚡ Immediately injected cached header');
        componentsInjected++;
    }

    // If we injected both components, mark as loaded immediately
    if (componentsInjected === 2) {
        document.body.classList.add('components-loaded');
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('components-ready');
        }
        console.log('⚡ Components ready immediately from cache');

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
})();

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const pageId = document.body.getAttribute('data-page') || 'unknown';

    if (pageId !== 'unknown') {
        // Initialize immediately for faster loading
        window.componentLoader.initializePage(pageId).then(() => {
            console.log('Components loaded successfully for page:', pageId);
            // Ensure main content is visible after successful load
            ensureMainContentVisible();
        }).catch((error) => {
            console.error('Failed to load components for page:', pageId, error);
            // Show main content even if components fail to load
            ensureMainContentVisible();
        });
    } else {
        // If no page ID, still show content after a short delay
        setTimeout(ensureMainContentVisible, 500);
    }
});

// Ensure main content is visible (fallback for loading issues)
function ensureMainContentVisible() {
    document.body.classList.remove('components-loading');
    document.body.classList.add('components-loaded');
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.classList.add('components-ready');
    }

    // Initialize Lucide icons if not already done
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Preload components immediately for faster subsequent loads
(function preloadComponents() {
    // Preload common components in the background
    if (window.componentLoader) {
        window.componentLoader.loadComponent('sidebar');
        window.componentLoader.loadComponent('header');
    } else {
        // Retry if componentLoader not ready yet
        setTimeout(preloadComponents, 100);
    }
})();

// Fallback timeout to ensure page loads even if components fail
setTimeout(function() {
    if (!document.body.classList.contains('components-loaded')) {
        console.log('⚠️ Component loading timeout - showing page anyway');
        ensureMainContentVisible();
    }
}, 500); // 500ms timeout

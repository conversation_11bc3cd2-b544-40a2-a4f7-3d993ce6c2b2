@echo off
echo ========================================
echo    PRESSURE MAX - QUICK DEV START
echo ========================================

:: Set working directory to script location
cd /d "%~dp0"

:: Start API server
echo 🔧 Starting API Server...
start "API Server" cmd /k "cd pressure-max-api && echo Starting API Server... && echo Testing Interface: http://localhost:3000/testing && echo Campaign Inspector: http://localhost:3000/testing (Campaign Raw Data Inspector section) && echo. && npm start"

:: Wait 3 seconds
timeout /t 3 /nobreak >nul

:: Start Frontend
echo 🎨 Starting Frontend...
start "Frontend" cmd /k "cd pressure-max-frontend && echo Starting Frontend... && echo Dashboard: http://localhost:3001 && echo. && npm start"

echo.
echo ✅ Services starting...
echo 📱 Frontend Dashboard: http://localhost:3001
echo 🔧 API Server: http://localhost:3000
echo 🧪 Testing Interface: http://localhost:3000/testing
echo 🔍 Campaign Inspector: Available in Testing Interface
echo.
echo Press any key to close this window...
pause >nul

import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { LogOut, User, Home, ArrowLeft } from 'lucide-react';
import AuthSection from '../components/AuthSection';
import FacebookConnectionSection from '../components/FacebookConnectionSection';
import CampaignSection from '../components/CampaignSection';
import LeadFormsSection from '../components/LeadFormsSection';
import ApiTestingSection from '../components/ApiTestingSection';

const Testing = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/landing');
  };

  const handleGoToLanding = () => {
    navigate('/landing');
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  if (!isAuthenticated) {
    navigate('/landing');
    return null;
  }

  return (
    <div className="dashboard">
      {/* Testing Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-title">
            <h1>🧪 API Testing Interface</h1>
            <p>Comprehensive testing interface for the Pressure Max API backend</p>
          </div>
          
          <div className="dashboard-user-section">
            {user && (
              <div className="user-info">
                <User size={16} />
                <span>Welcome, {user.firstName || user.email}</span>
              </div>
            )}
            
            <div className="dashboard-actions">
              <button onClick={handleGoToDashboard} className="nav-btn">
                <ArrowLeft size={16} />
                Dashboard
              </button>
              <button onClick={handleGoToLanding} className="nav-btn">
                <Home size={16} />
                Landing
              </button>
              <button onClick={handleLogout} className="logout-btn">
                <LogOut size={16} />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Testing Main Content */}
      <main className="dashboard-main">
        <div className="sections-container">
          <section className="section">
            <AuthSection />
          </section>

          <section className="section">
            <FacebookConnectionSection />
          </section>

          <section className="section">
            <CampaignSection />
          </section>

          <section className="section">
            <LeadFormsSection />
          </section>

          <section className="section">
            <ApiTestingSection />
          </section>
        </div>
      </main>

      {/* Testing Footer */}
      <footer className="dashboard-footer">
        <p>Pressure Max API Testing Interface - Built with React</p>
        <p>Backend API: <code>http://localhost:3000</code></p>
      </footer>
    </div>
  );
};

export default Testing;

@echo off
echo ========================================
echo    PRESSURE MAX - QUICK START
echo ========================================
echo.

:: Set working directory to script location
cd /d "%~dp0"

echo 🚀 Quick starting Pressure Max...
echo.

:: Start API server
echo 🔧 Starting Backend API Server (Port 3000)...
start "Pressure Max API" cmd /k "cd pressure-max-api && echo ========================================= && echo    PRESSURE MAX API SERVER && echo ========================================= && echo. && echo 🔧 Backend API: http://localhost:3000 && echo 🧪 Testing Interface: http://localhost:3000/testing && echo 🔍 Campaign Inspector: Available in Testing Interface && echo 📱 Facebook Ads: http://localhost:3000/facebook-ads && echo 🏥 Health Check: http://localhost:3000/health && echo. && npm start"

:: Wait for API to start
timeout /t 3 /nobreak >nul

:: Start Frontend
echo 🎨 Starting Frontend React App (Port 3001)...
start "Pressure Max Frontend" cmd /k "cd pressure-max-frontend && echo ========================================= && echo    PRESSURE MAX FRONTEND && echo ========================================= && echo. && echo 📱 Frontend Dashboard: http://localhost:3001 && echo 🔐 Login/Signup: http://localhost:3001/login && echo 🧪 Testing: http://localhost:3001/testing && echo. && npm start"

echo.
echo ✅ Both services are starting...
echo.
echo 📋 QUICK ACCESS LINKS:
echo    📱 Frontend Dashboard: http://localhost:3001
echo    🔧 Backend API: http://localhost:3000
echo    🧪 Testing Interface: http://localhost:3000/testing
echo    🔍 Campaign Inspector: http://localhost:3000/testing
echo    📱 Facebook Ads: http://localhost:3000/facebook-ads
echo.
echo 💡 TIP: The Campaign Raw Data Inspector is in the Testing Interface
echo 💡 TIP: Use Ctrl+C in each window to stop services
echo.
echo Press any key to close this launcher...
pause >nul
